<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="12dp">

        <TextView
            android:id="@+id/tvLeaveName"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textStyle="bold"
            android:text="User Name" />

        <TextView
            android:id="@+id/tvLeaveRollNumber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="Roll: 12345" />

        <TextView
            android:id="@+id/tvLeaveSubject"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:text="Computer Science" />

        <TextView
            android:id="@+id/tvLeaveSlot"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="9:00 AM - 11:00 AM" />

        <TextView
            android:id="@+id/tvLeaveSchool"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:text="Engineering School" />

        <TextView
            android:id="@+id/tvLeaveStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textStyle="italic"
            android:text="PENDING" />

        <LinearLayout
            android:id="@+id/actionButtonsLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end"
            android:layout_marginTop="8dp">

            <Button
                android:id="@+id/btnReject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Reject"
                android:textSize="12sp"
                android:backgroundTint="#F44336"
                style="@style/Widget.MaterialComponents.Button" />

            <Button
                android:id="@+id/btnApprove"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:text="Approve"
                android:textSize="12sp"
                android:backgroundTint="#4CAF50"
                style="@style/Widget.MaterialComponents.Button" />
        </LinearLayout>
    </LinearLayout>
</androidx.cardview.widget.CardView> 