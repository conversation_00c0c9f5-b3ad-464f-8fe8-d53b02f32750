# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8 -Djdk.http.auth.tunneling.disabledSchemes="" -Djdk.http.auth.proxying.disabledSchemes=""
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. For more details, visit
# https://developer.android.com/r/tools/gradle-multi-project-decoupled-projects
org.gradle.parallel=true
# AndroidX package structure to make it clearer which packages are bundled with the
# Android operating system, and which are packaged with your app's APK
# https://developer.android.com/topic/libraries/support-library/androidx-rn
android.useAndroidX=true
# Kotlin code style for this project: "official" or "obsolete":
kotlin.code.style=official
# Enables namespacing of each library's R class so that its R class includes only the
# resources declared in the library itself and none from the library's dependencies,
# thereby reducing the size of the R class for that library
android.nonTransitiveRClass=true

# --- Improved network settings ---
# Disable offline mode completely
org.gradle.offline=false
# Increase timeouts for network operations
systemProp.http.connectionTimeout=300000
systemProp.http.socketTimeout=300000
# Enable IPv4 stack only to avoid IPv6 issues
systemProp.java.net.preferIPv4Stack=true
# Set HTTP Keep-Alive to true
systemProp.http.keepAlive=true
# Increased daemon idle timeout to avoid daemon shutdowns
org.gradle.daemon.idletimeout=3600000
# Cache more aggressively
org.gradle.caching=true
# Add retry count for failed network operations
systemProp.http.retryCount=5
# Add connection request timeout
systemProp.http.connectionRequestTimeout=180000
# Allow redirects
systemProp.http.followRedirects=true
# Disable HTTPS certificate validation (only for troubleshooting)
# systemProp.ssl.TrustManagerFactory.algorithm=PKIX
# systemProp.javax.net.ssl.trustStoreType=JKS