<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:padding="2dp">

    <TextView
        android:id="@+id/dayNumber"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center"
        android:background="@drawable/calendar_day_background"
        android:gravity="center"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <View
        android:id="@+id/eventIndicator"
        android:layout_width="6dp"
        android:layout_height="6dp"
        android:layout_marginTop="2dp"
        android:background="@drawable/event_indicator"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/dayNumber"
        app:layout_constraintStart_toStartOf="@id/dayNumber"
        app:layout_constraintEnd_toEndOf="@id/dayNumber" />

</androidx.constraintlayout.widget.ConstraintLayout> 