import pandas as pd
import firebase_admin
from firebase_admin import credentials, firestore

# Initialize Firebase Admin SDK
cred = credentials.Certificate("serviceAccountKey.json")
firebase_admin.initialize_app(cred)
db = firestore.client()

# Read Excel file (ensure it's named students.xlsx)
df = pd.read_excel("students.xlsx")

# Rename columns to remove trailing spaces or dots
df.columns = [col.strip().replace(".", "").replace(" ", "_") for col in df.columns]

# Loop through rows and upload
for _, row in df.iterrows():
    data = row.to_dict()
    roll_no = str(data["Roll_No"])  # use roll number as document ID
    db.collection("students").document(roll_no).set(data)
    print(f"✅ Uploaded {roll_no}")

print("🎉 All student records uploaded successfully.")
