<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".LoginFormActivity">

    <!-- Dark top section with title and book icon -->
    <View
        android:id="@+id/topSection"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@drawable/login_background"
        app:layout_constraintHeight_percent="0.45"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imageViewBook"
        android:layout_width="60dp"
        android:layout_height="40dp"
        android:layout_marginTop="24dp"
        android:layout_marginStart="24dp"
        android:src="@drawable/ic_book"
        app:tint="#FFD700"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/textViewTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Teaching And Technical\nWing"
        android:textAlignment="center"
        android:textColor="#FFFFFF"
        android:textSize="32sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="@id/topSection"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3" />

    <TextView
        android:id="@+id/textViewTagline"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="8dp"
        android:text="Empowering Minds, Enriching Lives\nBridging Knowledge with Service"
        android:textAlignment="center"
        android:textColor="#CCFFFFFF"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/textViewTitle" />

    <!-- Light bottom section with login form -->
    <View
        android:id="@+id/bottomSection"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#E8E8E8"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topSection" />

    <TextView
        android:id="@+id/textViewLogIn"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="32dp"
        android:layout_marginTop="24dp"
        android:text="LOG IN"
        android:textColor="#222222"
        android:textSize="28sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/bottomSection" />

    <!-- Roll Number input -->
    <LinearLayout
        android:id="@+id/layoutRollNumber"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="24dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/textViewLogIn">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_myplaces"
            android:tint="#666666" />

        <EditText
            android:id="@+id/etRollNumber"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:background="@null"
            android:hint="Roll Number"
            android:inputType="text"
            android:padding="8dp"
            android:textColor="#333333"
            android:textSize="16sp" />
    </LinearLayout>

    <View
        android:id="@+id/divider1"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="8dp"
        android:background="#CCCCCC"
        app:layout_constraintTop_toBottomOf="@id/layoutRollNumber" />

    <!-- Email input -->
    <LinearLayout
        android:id="@+id/layoutEmail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/divider1">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_dialog_email"
            android:tint="#666666" />

        <EditText
            android:id="@+id/etEmail"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="@null"
            android:hint="<EMAIL>"
            android:inputType="textEmailAddress"
            android:padding="8dp"
            android:textColor="#333333"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/emailCheckMark"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_send"
            android:tint="#4CAF50"
            android:visibility="visible" />
    </LinearLayout>

    <View
        android:id="@+id/divider2"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="8dp"
        android:background="#CCCCCC"
        app:layout_constraintTop_toBottomOf="@id/layoutEmail" />

    <!-- Password input -->
    <LinearLayout
        android:id="@+id/layoutPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="16dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/divider2">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_lock_idle_lock"
            android:tint="#666666" />

        <EditText
            android:id="@+id/etPassword"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="@null"
            android:hint="**********"
            android:inputType="textPassword"
            android:padding="8dp"
            android:textColor="#333333"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/passwordVisibilityToggle"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_view"
            android:tint="#666666" />
    </LinearLayout>

    <View
        android:id="@+id/divider3"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="8dp"
        android:background="#CCCCCC"
        app:layout_constraintTop_toBottomOf="@id/layoutPassword" />

    <TextView
        android:id="@+id/textViewForgotPassword"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="32dp"
        android:text="Forgot Password?"
        android:textColor="#333333"
        android:textSize="14sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/divider3" />

    <Button
        android:id="@+id/btnLogin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32dp"
        android:layout_marginTop="32dp"
        android:background="@drawable/button_dark_bg"
        android:padding="12dp"
        android:text="Log In"
        android:textAllCaps="false"
        android:textColor="#FFFFFF"
        android:textSize="16sp"
        app:layout_constraintTop_toBottomOf="@id/textViewForgotPassword" />

    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:indeterminateTint="#333333"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btnLogin" />

    <!-- Back button - hidden visually but kept for functionality -->
    <ImageButton
        android:id="@+id/btnBack"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:src="@android:drawable/ic_menu_close_clear_cancel"
        android:tint="#FFFFFF"
        android:contentDescription="Back"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_marginTop="24dp"
        android:layout_marginEnd="24dp" />

</androidx.constraintlayout.widget.ConstraintLayout> 