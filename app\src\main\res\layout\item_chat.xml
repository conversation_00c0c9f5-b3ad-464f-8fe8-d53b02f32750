<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="12dp"
    android:background="@android:color/transparent"
    android:clickable="true"
    android:focusable="true">

    <ImageView
        android:id="@+id/image_profile"
        android:layout_width="50dp"
        android:layout_height="50dp"
        android:contentDescription="Profile picture"
        android:src="@android:drawable/ic_menu_report_image"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@android:color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toStartOf="@id/text_time"
        app:layout_constraintStart_toEndOf="@id/image_profile"
        app:layout_constraintTop_toTopOf="@id/image_profile"
        tools:text="Contact Name" />

    <TextView
        android:id="@+id/text_last_message"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="@android:color/darker_gray"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@id/badge_unread"
        app:layout_constraintStart_toEndOf="@id/image_profile"
        app:layout_constraintTop_toBottomOf="@id/text_name"
        app:layout_constraintBottom_toBottomOf="@id/image_profile"
        tools:text="Last message in the conversation" />

    <TextView
        android:id="@+id/text_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@android:color/darker_gray"
        android:textSize="12sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="@id/text_name"
        tools:text="10:30 AM" />

    <TextView
        android:id="@+id/badge_unread"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="@drawable/bg_circle"
        android:backgroundTint="@color/whatsapp_light_green"
        android:gravity="center"
        android:textColor="@android:color/white"
        android:textSize="12sp"
        android:textStyle="bold"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="@id/text_last_message"
        tools:text="3"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout> 