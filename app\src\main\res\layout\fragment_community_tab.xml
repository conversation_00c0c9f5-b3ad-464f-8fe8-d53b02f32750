<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <LinearLayout
        android:id="@+id/header_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="16dp"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Community"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/menu_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@android:drawable/ic_menu_more"
            android:tint="@color/white"
            android:clickable="true"
            android:focusable="true"
            android:contentDescription="Menu" />

    </LinearLayout>

    <TextView
        android:id="@+id/empty_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="No groups in community"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/groups_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/header_layout"
        app:layout_constraintBottom_toBottomOf="parent"
        android:paddingHorizontal="8dp"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout> 