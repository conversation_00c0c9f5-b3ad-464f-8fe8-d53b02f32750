<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="12dp"
    android:layout_marginVertical="6dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="#FFFFFF">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <TextView
            android:id="@+id/group_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="#0D0302"
            android:textSize="18sp"
            android:textStyle="bold"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/delete_button"
            app:layout_constraintHorizontal_bias="0.0"
            tools:text="Group 1" />

        <TextView
            android:id="@+id/group_id"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textColor="#808080"
            android:textSize="14sp"
            android:layout_marginTop="4dp"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/group_name"
            app:layout_constraintStart_toStartOf="@id/group_name"
            app:layout_constraintEnd_toStartOf="@id/delete_button"
            tools:text="ID: grp123" />

        <ImageButton
            android:id="@+id/delete_button"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:src="@android:drawable/ic_menu_delete"
            android:tint="#0D0302"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Delete group"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView> 