# UI Design System Documentation - Scheduling Module

## Color Palette & Theme

### Primary Colors

- **YellowAccent**: `#FFD600` - Primary accent color for buttons, icons, and highlights
- **DarkBackground**: `#121212` - Main background color for screens
- **DarkSurface**: `#1E1E1E` - Alternative surface color
- **NeutralCardSurface**: `#1F1F1F` - **PREFERRED** card background color for consistency
- **SurfaceElevated**: `#2C2C2C` - Elevated surface elements

### Secondary Colors

- **BlueAccent**: `#1E88E5` - Secondary accent
- **TealAccent**: `#00BCD4` - Tertiary accent (edit buttons)
- **SuccessGreen**: `#00C853` - Success states
- **ErrorRed**: `#FF5252` - Error states and delete actions
- **NeutralGray**: `#9E9E9E` - Neutral elements and borders

### Text Colors

- **Primary Text**: `#FFFFFF` (White) - Main text content
- **Secondary Text**: `#B0B0B0` (Light Gray) - Descriptions and secondary content
- **Disabled Text**: `#808080` - Disabled state text

## Layout & Spacing Standards

### Screen-Level Spacing

- **Horizontal Screen Margins**: 20dp (standard for all screens)
- **Vertical Screen Padding**: 24dp top, 16dp bottom
- **Content Padding**: 16dp horizontal for inner content areas

### Component Spacing

- **Card Spacing**: 16dp vertical spacing between cards
- **Section Spacing**: 24dp-32dp between major sections
- **Element Spacing**: 8dp-12dp between related elements
- **Button Spacing**: 8dp horizontal, 16dp vertical in groups

### Card Specifications

- **Corner Radius**: 16dp (standard for all cards)
- **Elevation**: 4dp default elevation for cards
- **Internal Padding**: 20dp-24dp for card content
- **Shadow**: 4dp elevation with optional colored shadow (YellowAccent.copy(alpha = 0.2f))

## Typography System

### Text Styles

- **Display Large**: Main screen titles (Schedule Maker)
- **Title Large**: Section headers and screen titles (22sp)
- **Title Medium**: Card titles and dialog headers
- **Body Large**: Interactive text, button labels (minimum 48dp touch targets)
- **Body Medium**: Standard content text
- **Body Small**: Secondary information, slot pills

### Font Weights

- **Bold**: Main titles, selected states
- **SemiBold/Medium**: Button text, important labels
- **Normal**: Standard content text

## Button Design System

### StandardButton Component

```kotlin
object StandardButtonDefaults {
    val Height = 44.dp
    val CornerRadius = 10.dp
    val Elevation = 3.dp
    val HorizontalPadding = 20.dp
    val VerticalPadding = 10.dp
    val MinimumWidth = 80.dp
}
```

### Button Specifications

- **Height**: 44dp minimum (48dp for accessibility compliance)
- **Corner Radius**: 10dp for buttons, 12dp for cards
- **Colors**: YellowAccent background, Black text
- **Elevation**: 3dp default, 2dp pressed, 0dp disabled
- **Content Padding**: 20dp horizontal, 10dp vertical
- **Minimum Width**: 80dp for readability

### Button States

- **Default**: YellowAccent background, black text, 3dp elevation
- **Pressed**: 2dp elevation, 0.98f scale
- **Disabled**: NeutralGray.copy(alpha = 0.5f) background, 0dp elevation

## Icon Specifications

### Icon Sizes

- **Large Icons**: 56dp circular containers with 24dp icons (menu cards)
- **Medium Icons**: 48dp containers with 24dp icons (action buttons, preset options delete)
- **Small Icons**: 36dp containers with 18dp icons (card delete buttons, inline actions)
- **Inline Icons**: 20dp for button icons, 18dp for text inline

### Icon Usage Guidelines

- **Card Delete Buttons**: Use Small Icons (36dp container, 18dp icon) for consistent card sizing
- **Options/Settings Delete**: Use Medium Icons (48dp container, 24dp icon) for standalone actions
- **Menu Actions**: Use Large Icons (56dp container, 24dp icon) for primary navigation

### Card Layout Consistency

- **Always Reserve Button Space**: Use Box containers to reserve space for optional buttons (delete, edit)
- **Consistent Card Heights**: All cards in a list should have identical dimensions regardless of button presence
- **Space Reservation Pattern**: `Box(modifier = Modifier.size(36.dp))` for small icon space, `Box(modifier = Modifier.size(48.dp))` for medium icon space

### Icon Colors

- **Primary**: Black on YellowAccent backgrounds
- **Secondary**: YellowAccent on dark backgrounds
- **Destructive**: ErrorRed (#FF5252) for delete actions
- **Edit**: TealAccent (#00BCD4) for edit actions

### Delete Button Implementation Patterns

#### Card Delete Buttons (Standard Pattern)

**Specifications:**

- **Container Size**: 36dp circular container
- **Icon Size**: 18dp delete icon
- **Background**: `Color.Red.copy(alpha = 0.2f)` with `CircleShape`
- **Icon Tint**: `Color(0xFFFF5252)` (ErrorRed)

**Implementation:**

```kotlin
IconButton(
    onClick = onDelete,
    modifier = Modifier
        .size(36.dp)
        .background(
            color = Color.Red.copy(alpha = 0.2f),
            shape = CircleShape
        )
) {
    Icon(
        imageVector = Icons.Default.Delete,
        contentDescription = "Delete",
        tint = Color(0xFFFF5252), // Red color for delete
        modifier = Modifier.size(18.dp)
    )
}
```

**Applied to:**

- TeachingSlotsScreen card delete buttons
- VolunteerPresetsScreen card delete buttons
- Any card-based delete actions for consistent UI

#### Standalone Delete Buttons (Alternative Pattern)

**Specifications:**

- **Container Size**: 48dp touch target
- **Icon Size**: 24dp delete icon
- **Background**: Transparent (no circular background)
- **Icon Tint**: `Color(0xFFFF5252)` (ErrorRed)

**Usage Guidelines:**

- **Card Delete**: Always use the circular background pattern for visual consistency
- **Standalone Actions**: Use transparent background for options/settings screens
- **Touch Targets**: Maintain 48dp minimum for accessibility compliance

## Interactive Elements

### Touch Targets

- **Minimum Size**: 48dp x 48dp for accessibility
- **Button Height**: 44dp minimum, 48dp preferred
- **Icon Buttons**: 48dp x 48dp containers
- **Card Touch Areas**: Full card area clickable

### Slot Pills (Time Slot Buttons)

- **Height**: 36dp
- **Corner Radius**: 18dp (pill-shaped)
- **Background**: YellowAccent gradient
- **Text**: Black, bodySmall, FontWeight.Medium
- **Padding**: 8dp horizontal
- **Elevation**: 2dp shadow
- **Spacing**: 8dp between pills

### Day Selection Buttons

- **Width**: 52dp
- **Corner Radius**: 12dp
- **Padding**: 8dp horizontal, 12dp vertical
- **Selected State**: YellowAccent background, black text, 4dp elevation
- **Unselected State**: Transparent background, white text, no elevation
- **Typography**: bodyLarge, bold when selected

## Input Field Specifications

### Text Fields & Dropdowns

- **Corner Radius**: 12dp-14dp for input fields
- **Border Colors**:
  - Focused: YellowAccent
  - Unfocused: NeutralGray.copy(alpha = 0.7f)
- **Text Colors**: White for both focused and unfocused states
- **Label Colors**: YellowAccent (focused), NeutralGray (unfocused)
- **Minimum Height**: 56dp for better touch targets
- **Minimum Width**: 160dp-170dp for dropdown visibility

### Number Input Fields

- **Height**: 56dp
- **Corner Radius**: 16dp
- **Background**: SurfaceElevated with animated color transitions
- **Text**: 28sp, FontWeight.Bold, White color
- **Elevation**: 2dp
- **Padding**: 16dp horizontal

## Dialog Specifications

### Save Dialog (CreateTeachingSlotsScreen)

- **Corner Radius**: 20dp for modern appearance
- **Background**: NeutralCardSurface
- **Elevation**: 12dp for prominence
- **Padding**: 20dp internal padding
- **Button Height**: 48dp for accessibility
- **Button Spacing**: 20dp between buttons

### Alert Dialogs

- **Corner Radius**: 16dp
- **Background**: DarkSurface
- **Button Corner Radius**: 8dp
- **Confirm Button**: Red background for destructive actions
- **Cancel Button**: YellowAccent text color

## List & Grid Layouts

### LazyColumn Specifications

- **Item Spacing**: 16dp vertical spacing
- **Content Padding**: 16dp vertical, 20dp horizontal
- **Horizontal Margins**: 20dp from screen edges

### Grid Layouts (Teaching Slots)

- **Column Spacing**: 8dp between grid items
- **Row Spacing**: 8dp between grid rows
- **Item Padding**: 8dp internal padding
- **Maximum Items Per Row**: 3 for optimal readability

### Editable Cell Visual Distinction (SetAvailabilityScreen)

**Cell State Colors:**

- **Editable Cells**: `Color(0xFF4A4A4A)` - Lighter gray for clear distinction
- **Non-Editable Cells**: `DarkSurface.copy(alpha = 0.3f)` - Darker for contrast
- **Selected/Filled Cells**: `YellowAccent` - Bright yellow for active state

**Implementation Pattern:**

```kotlin
.background(
    color = if (active) {
        val hasValue = // check if cell has data
        if (hasValue) {
            YellowAccent // Filled state
        } else {
            Color(0xFF4A4A4A) // Editable but empty
        }
    } else {
        DarkSurface.copy(alpha = 0.3f) // Non-editable
    }
)
```

**Usage Guidelines:**

- **High Contrast**: Ensure clear visual distinction between editable and non-editable states
- **Accessibility**: Maintain sufficient contrast ratios for all text content
- **Consistency**: Use these exact color values across all grid-based input screens

## Loading & Error States

### Loading Indicators

- **Size**: 48dp circular progress indicator
- **Color**: YellowAccent
- **Stroke Width**: 4dp
- **Animation**: fadeIn + scaleIn (300ms duration)

### Error States

- **Card Background**: DarkSurface
- **Corner Radius**: 16dp
- **Elevation**: 8dp shadow
- **Padding**: 24dp
- **Animation**: fadeIn + slideInVertically + scaleIn

### Empty States

- **Card Background**: DarkSurface
- **Corner Radius**: 16dp
- **Elevation**: 8dp shadow
- **Padding**: 24dp
- **Animation**: 150ms delay + fadeIn + slideInVertically + scaleIn

## Navigation & Header Specifications

### Top App Bar

- **Background**: Transparent
- **Title Font Size**: 22sp
- **Title Font Weight**: Bold
- **Title Color**: White
- **Title Padding**: 8dp start padding
- **Back Button Size**: 48dp container, 28dp icon
- **Action Button**: StandardButton component in top bar
- **TopAppBar Padding**: 8dp top and bottom padding
- **Content Spacing**: 20dp horizontal margins, 24dp top vertical padding for main content

### Navigation Icons

- **Back Arrow**: 28dp size, White color
- **Container Size**: 48dp for accessibility
- **Padding**: 8dp around container

### Header Save Button Pattern

**Standard header layout with save button (CreateTeachingSlotsScreen, SetAvailabilityScreen):**

```kotlin
Row(
    modifier = Modifier
        .fillMaxWidth()
        .padding(bottom = 16.dp),
    verticalAlignment = Alignment.CenterVertically
) {
    // Back button
    IconButton(onClick = { navController.navigateUp() }) { ... }

    // Title
    Text(
        text = "Screen Title",
        modifier = Modifier.weight(1f)
    )

    // Save button - only show when data is loaded
    if (dataLoaded && !isLoading) {
        StandardButton(onClick = { saveData() }) {
            Text("Save", fontWeight = FontWeight.Medium)
        }
    }
}
```

**Usage Guidelines:**

- **Positioning**: Save button positioned in top-right corner of header
- **Conditional Display**: Only show when relevant data is loaded and not in loading state
- **Button Style**: Use StandardButton component for consistency
- **Text**: Simple "Save" text with Medium font weight
- **Spacing**: No additional padding needed, StandardButton handles internal spacing

---

## Animation System Specifications

### Core Animation Timing Pattern (UI.md Standard)

**All scheduling module screens follow this standardized timing sequence:**

```kotlin
LaunchedEffect(Unit) {
    if (animationsEnabled) {
        delay(100)           // Initial delay
        headerVisible = true // Header/top bar appears
        delay(200)           // Main delay
        contentVisible = true // Main content appears
        delay(100)           // Final delay
        listVisible = true   // List/grid content appears
    }
}
```

**Timing Sequence**: 100ms → 200ms → 100ms delays (consistent across all screens)

### Animation Duration Standards

#### Container & Section Animations

- **Overall Container**:

  - slideInVertically: 400ms duration
  - fadeIn: 300ms duration
  - initialOffsetY: `it / 4` (25% offset)

- **Header/Top Bar**:

  - fadeIn: 500ms duration
  - delayMillis: 100ms additional delay
  - No slide animation (fade only for stability)

- **Content Sections**:

  - slideInVertically: 400ms duration
  - fadeIn: 300ms duration
  - initialOffsetY: `it / 4` (25% offset)

- **List/Grid Content**:
  - slideInVertically: 300ms duration
  - fadeIn: 250ms duration
  - initialOffsetY: `it / 2` (50% offset)

### Animation Timeout System (1.5-Second Rule)

**Purpose**: Prevent animations after 1.5 seconds from when content becomes visible to improve user experience and performance.

**Implementation Pattern**:

```kotlin
// Animation control flag for accessibility compliance
var animationsEnabled by remember { mutableStateOf(true) }

// 1.5-second animation timeout flag - starts AFTER loading completes
LaunchedEffect(isLoading) {
    if (!isLoading) {
        // Start with animations enabled when loading completes
        animationsEnabled = true

        // Disable animations after 1.5 seconds from when loading completes
        kotlinx.coroutines.delay(1500)
        animationsEnabled = false
    }
}
```

**Key Features**:

- **Loading-Aware**: Timer starts only after `isLoading = false`
- **Content-Based**: 1.5 seconds from when content is actually visible
- **Automatic Reset**: Resets on screen revisit or data reload
- **Performance**: Prevents scroll-triggered animations after timeout

**Applied to**:

- ✅ TeachingSlotsOptionsScreen (Select Teaching Slots Preset)
- ✅ TeachingSlotsScreen (Teaching Slots list)
- All scheduling module screens with loading states

**Timeline Example**:

- `0s`: Screen opens, loading starts
- `~2-3s`: Loading completes (`isLoading = false`), content appears, animations enabled
- `~2-3s + 1.5s`: Animations disabled, no more transitions allowed

#### Individual Element Animations

- **Menu Buttons**: 300ms slideInVertically + 250ms fadeIn
- **Cards**: 300ms slideInVertically + 250ms fadeIn
- **Dialog Elements**: 300ms slideInVertically + 250ms fadeIn + 300ms scaleIn

### Staggered Animation Patterns

#### Menu Button Staggering (ScheduleMakerScreen)

1. **Create Teaching Slots**: 0ms delay
2. **Set Availability**: 100ms delay
3. **Manage Volunteers**: 200ms delay
4. **Generate Schedule**: 300ms delay
5. **Assign Curriculum**: 400ms delay
6. **View Assignments**: 500ms delay

**Pattern**: 100ms incremental delay between buttons

#### Enhanced List Item Staggering (Standard Pattern)

**Primary Pattern for List Items/Cards:**

- **Base Delay**: 200ms (initial preparation delay)
- **Incremental Delay**: 150ms per item
- **Maximum Delay**: 800ms to prevent excessive delays
- **Calculation**: `baseDelay + (index * 150).coerceAtMost(800)`

**Applied to:**

- TeachingSlotsScreen card list
- EditTeachingSlotsScreen form elements
- SetAvailabilityScreen grid items
- Any list-based content in scheduling module

**Legacy Pattern (Deprecated):**

- **Delay Calculation**: `(index * 100).coerceAtMost(500)`
- **Use Case**: Only for simple menu buttons or non-primary content

#### ScheduleMakerScreen-Style Card Staggering (TeachingSlotsScreen)

**New Simplified Pattern for Card Lists:**

- **Incremental Delay**: 150ms per card (0ms, 150ms, 300ms, 450ms...)
- **No Base Delay**: Cards start appearing immediately when list becomes visible
- **Calculation**: `index * 150` (no maximum cap for smoother experience)
- **Component Pattern**: Individual `StaggeredCard` components with own animation state

**Implementation Approach:**

```kotlin
// Individual card component with staggered entrance
@Composable
fun StaggeredTeachingSlotCard(
    delay: Int,
    animationsEnabled: Boolean,
    slot: TeachingSlotItem,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        if (animationsEnabled) {
            kotlinx.coroutines.delay(delay.toLong())
        }
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
            animationSpec = tween(
                durationMillis = if (animationsEnabled) 300 else 0,
                easing = FastOutSlowInEasing
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = if (animationsEnabled) 250 else 0,
                easing = FastOutSlowInEasing
            )
        )
    ) {
        // Actual card content
        TeachingSlotCard(...)
    }
}

// Usage in LazyColumn
itemsIndexed(teachingSlots) { index, slot ->
    StaggeredTeachingSlotCard(
        delay = if (animationsEnabled.value) index * 150 else 0,
        animationsEnabled = animationsEnabled.value,
        slot = slot,
        onClick = { ... },
        onDelete = { ... }
    )
}
```

**Key Differences from Enhanced Pattern:**

- **Simpler State Management**: Each card manages its own `isVisible` state
- **No Animation Tracking**: No need for `animatedItems` set or complex reset logic
- **Direct Delay Calculation**: `index * 150` without base delay or maximum cap
- **Component-Based**: Follows ScheduleMakerScreen's `StaggeredMenuButton` pattern
- **Cleaner Reset**: Animation state resets automatically on recomposition

**Applied to:**

- TeachingSlotsScreen card list (primary implementation)
- Any screen requiring ScheduleMakerScreen-style smooth staggered animations

#### Dialog Content Staggering (CreateTeachingSlotsScreen)

1. **Dialog Container**: 50ms delay
2. **Title Content**: 100ms delay
3. **Form Elements**: 150ms delay (staggered by 50ms each)
4. **Buttons**: 250ms delay

### Color Animation Specifications

#### Card Background Transitions

```kotlin
val animatedCardColor by animateColorAsState(
    targetValue = NeutralCardSurface,
    animationSpec = tween(
        durationMillis = if (animationsEnabled) 300 else 0,
        easing = FastOutSlowInEasing
    ),
    label = "card_background_color"
)
```

#### Input Field Color Transitions

```kotlin
val animatedInputColor by animateColorAsState(
    targetValue = SurfaceElevated,
    animationSpec = tween(
        durationMillis = if (animationsEnabled) 300 else 0,
        easing = FastOutSlowInEasing
    ),
    label = "input_field_color"
)
```

### Interactive Animation Specifications

#### Button Press Animations

- **Scale**: 0.98f (2% reduction)
- **Duration**: 100ms
- **Animation Spec**: `tween(durationMillis = 100)`
- **Applied To**: All buttons (StandardButton, menu cards, etc.)

#### Loading State Animations

- **Circular Progress**: fadeIn + scaleIn (300ms)
- **Initial Scale**: 0.8f
- **Easing**: FastOutSlowInEasing

#### Error State Animations

- **Entry**: fadeIn + slideInVertically + scaleIn (300ms)
- **Initial Scale**: 0.9f
- **Slide Offset**: `it / 3` (33% offset)
- **Easing**: FastOutSlowInEasing

### Animation Easing Functions

#### Primary Easing

- **FastOutSlowInEasing**: Used for color transitions and scale animations
- **Default Tween**: Used for slide and fade animations (equivalent to FastOutSlowInEasing)

#### Animation Types by Use Case

1. **fadeIn()**: Visibility transitions, opacity changes
2. **slideInVertically()**: Entrance animations, content reveals
3. **scaleIn()**: Emphasis animations, dialog entrances
4. **animateColorAsState()**: Background color transitions
5. **animateFloatAsState()**: Scale animations, press feedback

### Accessibility & Performance

#### Animation Control

- **animationsEnabled Flag**: All animations respect this boolean
- **Graceful Degradation**: When disabled, all durations become 0ms
- **Offset Override**: When disabled, all offsets become 0
- **Immediate Display**: All content appears instantly when animations disabled

#### Performance Optimizations

- **Enhanced Maximum Stagger Delay**: 800ms for primary content, 500ms for secondary content
- **Efficient Transitions**: Use of `remember` for animation states
- **Conditional Animation**: Only animate when `animationsEnabled = true`
- **Base Delay Pattern**: 200ms preparation delay before staggered animations begin

### Standardized Animation Implementation Templates

#### Standard Card Entrance

```kotlin
AnimatedVisibility(
    visible = isVisible,
    enter = slideInVertically(
        initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
        animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
    ) + fadeIn(
        animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
    )
)
```

#### Standard Button Press

```kotlin
val scale by animateFloatAsState(
    targetValue = if (isPressed) 0.98f else 1f,
    animationSpec = tween(durationMillis = 100),
    label = "button_scale"
)
```

#### Standard Color Transition

```kotlin
val animatedColor by animateColorAsState(
    targetValue = targetColor,
    animationSpec = tween(
        durationMillis = if (animationsEnabled) 300 else 0,
        easing = FastOutSlowInEasing
    ),
    label = "color_transition"
)
```

### Animation Reset Patterns for Navigation Consistency

#### Robust Animation Reset Solution

**Problem**: Animation states persist across navigation, causing animations to fail on subsequent screen visits.

**Solution**: Use incremental animation trigger pattern for guaranteed state reset.

```kotlin
// Animation trigger that increments on each screen visit
var animationTrigger by remember { mutableStateOf(0) }

// Main animation sequence with reset
LaunchedEffect(Unit) {
    // Increment trigger to invalidate all card animations
    animationTrigger++

    // Always reset animation states on screen entry
    headerVisible = false
    contentVisible = false
    listVisible = false

    if (animationsEnabled) {
        delay(100)           // Initial delay
        headerVisible = true // Header/top bar appears
        delay(200)           // Main delay
        contentVisible = true // Main content appears
        delay(100)           // Final delay
        listVisible = true   // List/grid content appears
    } else {
        // Immediate display when animations disabled
        headerVisible = true
        contentVisible = true
        listVisible = true
    }
}
```

#### Individual Card Animation Reset

```kotlin
// Individual card animation state - reset when animation trigger changes
val cardVisible = remember(animationTrigger) { mutableStateOf(false) }

// Trigger animation when list becomes visible - using the trigger ensures fresh state
LaunchedEffect(listVisible, animationTrigger) {
    if (listVisible) {
        if (animationsEnabled) {
            kotlinx.coroutines.delay(cardDelay.toLong())
        }
        cardVisible.value = true
    }
}
```

#### Enhanced Staggered Card Entrance with Reset

```kotlin
// Enhanced staggered animation delay calculation (NEW STANDARD)
val baseDelay = if (animationsEnabled) 200 else 0 // Preparation delay
val cardDelay = if (animationsEnabled) baseDelay + (index * 150).coerceAtMost(800) else 0

// Individual card animation state - reset when animation trigger changes
val cardVisible = remember(animationTrigger) { mutableStateOf(false) }

// Trigger animation when list becomes visible
LaunchedEffect(listVisible, animationTrigger) {
    // Reset animation state first
    cardVisible.value = false

    if (listVisible) {
        if (animationsEnabled) {
            kotlinx.coroutines.delay(cardDelay.toLong())
        }
        cardVisible.value = true
    }
}

// Animated card entrance with UI.md specifications
AnimatedVisibility(
    visible = cardVisible.value,
    enter = slideInVertically(
        initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
        animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
    ) + fadeIn(
        animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
    )
) {
    // Card content
}
```

**Usage Guidelines:**

- **Apply to All Screens**: Every screen with animations should use this pattern
- **Guaranteed Reset**: `animationTrigger++` ensures fresh state on every screen visit
- **Consistent Behavior**: Animations work identically on first visit and all subsequent visits
- **Memory Efficient**: Old animation states are automatically garbage collected
- **Performance Safe**: Minimal overhead with automatic cleanup

### TeachingSlotsScreen Animation Architecture

#### Screen-Level Animation Sequence

**Timing Pattern**: 100ms → 200ms → 100ms (UI.md standard)

```kotlin
// Animation states for different UI elements
var topBarVisible by remember { mutableStateOf(false) }
var contentVisible by remember { mutableStateOf(false) }
var listVisible by remember { mutableStateOf(false) }

// Animation reset trigger
var animationTrigger by remember { mutableStateOf(0) }

// Animation control with 800ms timeout
val animationsEnabled = remember { mutableStateOf(true) }

LaunchedEffect(Unit) {
    kotlinx.coroutines.delay(800)
    animationsEnabled.value = false
}

// Screen entrance sequence
LaunchedEffect(animationTrigger) {
    topBarVisible = false
    contentVisible = false
    listVisible = false

    if (animationsEnabled.value) {
        kotlinx.coroutines.delay(100)  // Top bar appears
        topBarVisible = true
        kotlinx.coroutines.delay(200)  // Content appears
        contentVisible = true
        kotlinx.coroutines.delay(100)  // List container appears
        listVisible = true
    } else {
        // Immediate display when animations disabled
        topBarVisible = true
        contentVisible = true
        listVisible = true
    }
}
```

#### Card-Level Animation Implementation

**Pattern**: ScheduleMakerScreen-style individual component animation

```kotlin
// Individual card with staggered entrance
@Composable
fun StaggeredTeachingSlotCard(
    delay: Int,
    animationsEnabled: Boolean,
    slot: TeachingSlotItem,
    onClick: () -> Unit,
    onDelete: () -> Unit,
    modifier: Modifier = Modifier
) {
    var isVisible by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        if (animationsEnabled) {
            kotlinx.coroutines.delay(delay.toLong())
        }
        isVisible = true
    }

    AnimatedVisibility(
        visible = isVisible,
        enter = slideInVertically(
            initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
            animationSpec = tween(
                durationMillis = if (animationsEnabled) 300 else 0,
                easing = FastOutSlowInEasing
            )
        ) + fadeIn(
            animationSpec = tween(
                durationMillis = if (animationsEnabled) 250 else 0,
                easing = FastOutSlowInEasing
            )
        )
    ) {
        TeachingSlotCard(
            slot = slot,
            onClick = onClick,
            onDelete = onDelete,
            modifier = modifier
        )
    }
}
```

#### Card Interaction Animations

**Press Feedback**: 0.98f scale + background color transition

```kotlin
@Composable
fun TeachingSlotCard(...) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()

    // Scale animation for press feedback
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.98f else 1f,
        animationSpec = tween(durationMillis = 100),
        label = "card_scale"
    )

    // Background color animation
    val backgroundColor by animateColorAsState(
        targetValue = if (isPressed) NeutralCardSurface.copy(alpha = 0.8f) else NeutralCardSurface,
        animationSpec = tween(
            durationMillis = 200,
            easing = FastOutSlowInEasing
        ),
        label = "card_background"
    )

    Card(
        modifier = modifier
            .fillMaxWidth()
            .scale(scale)
            .clickable(
                interactionSource = interactionSource,
                indication = null
            ) { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = backgroundColor
        )
    ) {
        // Card content
    }
}
```

#### Animation Timing Summary

1. **Screen Entry**: 100ms → 200ms → 100ms sequence
2. **Card Staggering**: 150ms incremental delays (0ms, 150ms, 300ms, 450ms...)
3. **Card Entrance**: 300ms slideInVertically + 250ms fadeIn
4. **Press Feedback**: 100ms scale animation + 200ms color transition
5. **Animation Timeout**: 800ms after which all animations are disabled

**Key Benefits:**

- **Smooth Visual Flow**: Cards appear in elegant sequence
- **Responsive Interactions**: Immediate press feedback
- **Performance Optimized**: Automatic animation disable after 800ms
- **Accessibility Compliant**: All animations can be disabled
- **Memory Efficient**: Component-based state management

---

## Implementation Guidelines & Best Practices

### Component Usage Standards

#### Mandatory Components

- **StandardButton**: Use for all primary actions (Save, New, etc.)
- **NeutralCardSurface**: Use as default card background color
- **Material3 Card**: Use for all card containers with consistent elevation
- **AnimatedVisibility**: Use for all entrance/exit animations

#### Color Implementation

- **Always use theme colors**: Reference Color.kt constants, never hardcode hex values
- **Consistent backgrounds**: NeutralCardSurface (#1F1F1F) for cards, DarkBackground (#121212) for screens
- **Proper contrast**: Ensure YellowAccent (#FFD600) icons with black text, white text on dark backgrounds

### Animation Implementation Requirements

#### Mandatory Animation Support

- **animationsEnabled Flag**: Every screen must implement and respect this boolean
- **Graceful Degradation**: All animations must work when disabled (0ms durations, 0 offsets)
- **Consistent Timing**: Follow 100ms → 200ms → 100ms delay pattern across all screens
- **Staggered Elements**: Use 100ms incremental delays for lists and button groups

#### Animation Performance

- **Maximum Delays**: Cap staggered delays at 500ms for complex patterns, no cap for ScheduleMakerScreen-style patterns
- **Efficient State Management**: Use `remember` for animation states, prefer component-based state for card lists
- **Conditional Execution**: Only execute animation logic when `animationsEnabled = true`
- **Pattern Selection**: Use ScheduleMakerScreen-style for card lists, Enhanced pattern for complex forms

### Accessibility Compliance

#### Touch Targets

- **Minimum Size**: 48dp x 48dp for all interactive elements
- **Button Heights**: 44dp minimum, 48dp preferred for better accessibility
- **Icon Buttons**: Always use 48dp containers regardless of icon size
- **Spacing**: Ensure adequate spacing between interactive elements (8dp minimum)

#### Typography & Contrast

- **Body Large**: Use for all interactive text to ensure readability
- **High Contrast**: White text on dark backgrounds, black text on yellow backgrounds
- **Font Weights**: Bold for selected states, Medium for interactive elements

### Layout & Spacing Consistency

#### Screen-Level Standards

- **Horizontal Margins**: 20dp standard for all screens
- **Vertical Padding**: 24dp top, 16dp bottom for content areas
- **Card Spacing**: 16dp between cards in lists
- **Section Spacing**: 24dp-32dp between major UI sections

#### Component-Level Standards

- **Card Padding**: 20dp-24dp internal padding for content
- **Button Spacing**: 8dp horizontal, 16dp vertical in groups
- **Element Spacing**: 8dp-12dp between related elements
- **Input Field Spacing**: 16dp between form fields

### Error Handling & States

#### Loading States

- **Consistent Indicators**: 48dp YellowAccent CircularProgressIndicator
- **Animation**: fadeIn + scaleIn with 300ms duration
- **Positioning**: Center-aligned with proper spacing

#### Error States

- **Card Background**: DarkSurface with 16dp corner radius
- **Animation**: fadeIn + slideInVertically + scaleIn
- **Action Buttons**: Use StandardButton for retry actions

#### Empty States

- **Consistent Messaging**: Clear, actionable text with create buttons
- **Animation**: 150ms delay + standard entrance animations
- **Call-to-Action**: Always provide StandardButton for primary action

### Testing & Quality Assurance

#### Animation Testing

- **Device Performance**: Test on various device speeds and capabilities
- **Accessibility**: Verify animations can be disabled and still provide good UX
- **Timing Verification**: Ensure staggered animations don't feel sluggish

#### Visual Consistency

- **Color Accuracy**: Verify all colors match the defined palette
- **Spacing Verification**: Use layout inspection tools to verify dp values
- **Typography Consistency**: Ensure font sizes and weights match specifications

#### Interaction Testing

- **Touch Target Verification**: Ensure all interactive elements meet 48dp minimum
- **Button Feedback**: Verify press animations work consistently
- **Navigation Flow**: Test back button and navigation consistency

### Code Organization

#### File Structure

- **Consistent Imports**: Always import theme colors from Color.kt
- **Component Separation**: Keep reusable components in separate files
- **Animation Logic**: Group animation states and logic together

#### Naming Conventions

- **Animation States**: Use descriptive names (headerVisible, contentVisible, listVisible)
- **Color Variables**: Use descriptive names for animated colors (animatedCardColor)
- **Component Props**: Use consistent parameter names across similar components

### Future Considerations

#### Scalability

- **Component Library**: Consider extracting common patterns into reusable components
- **Theme Extensions**: Plan for potential theme variations while maintaining consistency
- **Animation Library**: Consider creating animation utility functions for common patterns

#### Maintenance

- **Documentation Updates**: Keep UI.md updated when making design changes
- **Version Control**: Track design system changes and their impact
- **Cross-Platform**: Consider how specifications might apply to other platforms

## Student Selection Interface Specifications

### Table Layout Standards (Edit: FA Screen Pattern)

#### Search Bar Specifications

- **Background**: NeutralCardSurface (#1F1F1F)
- **Corner Radius**: 16dp
- **Height**: 56dp minimum for accessibility
- **Horizontal Margins**: 20dp from screen edges
- **Vertical Margins**: 16dp top and bottom
- **Placeholder Text**: "Search by name, roll number or group"
- **Text Color**: White (#FFFFFF)
- **Icon Color**: YellowAccent (#FFD600)

#### Table Header Specifications

- **Background**: DarkSurface (#1E1E1E)
- **Height**: 48dp minimum
- **Text Style**: titleMedium, FontWeight.Bold
- **Text Color**: YellowAccent (#FFD600)
- **Padding**: 16dp horizontal, 12dp vertical
- **Column Widths**:
  - Select: 60dp (checkbox column)
  - Name: Flexible weight(2f)
  - Roll No.: 120dp fixed
  - Group: 80dp fixed

#### Student Row Specifications

- **Background**: NeutralCardSurface (#1F1F1F)
- **Corner Radius**: 12dp
- **Elevation**: 2dp
- **Height**: 64dp minimum for accessibility
- **Margin**: 16dp vertical spacing between rows
- **Horizontal Margins**: 20dp from screen edges
- **Padding**: 16dp horizontal, 12dp vertical

#### Checkbox Specifications

- **Container Size**: 48dp x 48dp for accessibility
- **Checkbox Size**: 24dp
- **Colors**:
  - Checked: YellowAccent (#FFD600)
  - Unchecked: NeutralGray (#9E9E9E)
  - Checkmark: Black (#000000)
- **Positioning**: Center-aligned in 60dp column

#### Text Specifications

- **Name Text**:
  - Style: bodyLarge
  - Color: White (#FFFFFF)
  - FontWeight: Medium
- **Roll Number Text**:
  - Style: bodyMedium
  - Color: Secondary Text (#B0B0B0)
  - FontWeight: Normal
- **Group Text**:
  - Style: bodyMedium
  - Color: Secondary Text (#B0B0B0)
  - FontWeight: Normal

#### Selection State Visual Feedback

- **Selected Row Background**: YellowAccent.copy(alpha = 0.1f)
- **Selected Text Color**: White (#FFFFFF) with increased opacity
- **Unselected Text Color**: Standard text colors with normal opacity
- **Transition Animation**: 200ms color transition using animateColorAsState

#### Implementation Template

```kotlin
@Composable
fun StudentSelectionScreen(
    students: List<Student>,
    selectedStudents: Set<String>,
    onStudentToggle: (String) -> Unit,
    onSearchQueryChange: (String) -> Unit,
    searchQuery: String = ""
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(DarkBackground)
            .padding(horizontal = 20.dp)
    ) {
        // Search Bar
        OutlinedTextField(
            value = searchQuery,
            onValueChange = onSearchQueryChange,
            placeholder = {
                Text(
                    "Search by name, roll number or group",
                    color = NeutralGray
                )
            },
            leadingIcon = {
                Icon(
                    Icons.Default.Search,
                    contentDescription = "Search",
                    tint = YellowAccent
                )
            },
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp)
                .height(56.dp),
            shape = RoundedCornerShape(16.dp),
            colors = OutlinedTextFieldDefaults.colors(
                focusedContainerColor = NeutralCardSurface,
                unfocusedContainerColor = NeutralCardSurface,
                focusedTextColor = Color.White,
                unfocusedTextColor = Color.White
            )
        )

        // Table Header
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 8.dp),
            shape = RoundedCornerShape(12.dp),
            colors = CardDefaults.cardColors(containerColor = DarkSurface),
            elevation = CardDefaults.cardElevation(defaultElevation = 2.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp)
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Select",
                    modifier = Modifier.width(60.dp),
                    style = MaterialTheme.typography.titleMedium,
                    color = YellowAccent,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Name",
                    modifier = Modifier.weight(2f),
                    style = MaterialTheme.typography.titleMedium,
                    color = YellowAccent,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Roll No.",
                    modifier = Modifier.width(120.dp),
                    style = MaterialTheme.typography.titleMedium,
                    color = YellowAccent,
                    fontWeight = FontWeight.Bold
                )
                Text(
                    text = "Group",
                    modifier = Modifier.width(80.dp),
                    style = MaterialTheme.typography.titleMedium,
                    color = YellowAccent,
                    fontWeight = FontWeight.Bold
                )
            }
        }

        // Student List
        LazyColumn(
            verticalArrangement = Arrangement.spacedBy(16.dp),
            contentPadding = PaddingValues(bottom = 16.dp)
        ) {
            items(students) { student ->
                StudentSelectionRow(
                    student = student,
                    isSelected = selectedStudents.contains(student.rollNumber),
                    onToggle = { onStudentToggle(student.rollNumber) }
                )
            }
        }
    }
}

@Composable
fun StudentSelectionRow(
    student: Student,
    isSelected: Boolean,
    onToggle: () -> Unit
) {
    val backgroundColor by animateColorAsState(
        targetValue = if (isSelected) {
            YellowAccent.copy(alpha = 0.1f)
        } else {
            NeutralCardSurface
        },
        animationSpec = tween(durationMillis = 200),
        label = "row_background"
    )

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onToggle() },
        shape = RoundedCornerShape(12.dp),
        color = backgroundColor,
        shadowElevation = 0.dp, // No shadow/elevation to eliminate any border effects
        border = null // Remove any border styling for cleaner visual design
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .heightIn(min = 64.dp)
                .padding(horizontal = 16.dp, vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Checkbox with proper touch target
            Box(
                modifier = Modifier
                    .size(48.dp)
                    .width(60.dp),
                contentAlignment = Alignment.Center
            ) {
                Checkbox(
                    checked = isSelected,
                    onCheckedChange = { onToggle() },
                    colors = CheckboxDefaults.colors(
                        checkedColor = YellowAccent,
                        uncheckedColor = NeutralGray,
                        checkmarkColor = Color.Black
                    )
                )
            }

            // Student Name
            Text(
                text = student.name,
                modifier = Modifier.weight(2f),
                style = MaterialTheme.typography.bodyLarge,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )

            // Roll Number
            Text(
                text = student.rollNumber,
                modifier = Modifier.width(120.dp),
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFB0B0B0)
            )

            // Group
            Text(
                text = student.group.toString(),
                modifier = Modifier.width(80.dp),
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFB0B0B0)
            )
        }
    }
}
```

#### Animation Specifications for Student Selection

- **Row Entrance**: 300ms slideInVertically + 250ms fadeIn
- **Selection State**: 200ms color transition using animateColorAsState
- **Search Results**: 150ms fadeIn for filtered results
- **Staggered Loading**: 100ms incremental delays for initial list display
- **Visual Design**: Uses Surface component with `shadowElevation = 0.dp` and `border = null` to completely eliminate white outlines/borders for cleaner appearance while maintaining yellow background tint for selected volunteers

#### One-Time Animation System

**Implementation**: All list-based screens use `animatedItems` tracking set to prevent re-animation during scrolling:

```kotlin
// Track which items have been animated to prevent re-animation during scrolling
val animatedItems = remember { mutableSetOf<String>() }

// Check if this item has already been animated
val hasBeenAnimated = animatedItems.contains(item.id)

LaunchedEffect(item.id, listVisible, animationTrigger) {
    // Only animate if item hasn't been animated before and list is visible
    if (!hasBeenAnimated && listVisible) {
        // Reset animation state first
        itemVisible.value = false

        if (animationsEnabled) {
            kotlinx.coroutines.delay(itemDelay.toLong())
        }
        itemVisible.value = true

        // Mark this item as animated
        animatedItems.add(item.id)
    } else if (hasBeenAnimated) {
        // Item already animated, show immediately
        itemVisible.value = true
    }
}
```

**Behavior**:

- Items animate in with standard entrance animations (300ms slideInVertically + 250ms fadeIn) only on first appearance
- Once animated, items remain static during subsequent scrolling (up or down)
- Animation tracking is cleared when screen is refreshed/revisited via `animatedItems.clear()`
- Maintains all existing animation timing and staggered delay patterns

---

## Schedule Creation Screen Specifications

### Header Layout Pattern (ScheduleCreationScreen)

**Clean Header with Single Action:**

```kotlin
Row(
    modifier = Modifier
        .fillMaxWidth()
        .padding(
            horizontal = 20.dp, // UI.md standard horizontal margins
            vertical = 8.dp     // UI.md header padding
        ),
    verticalAlignment = Alignment.CenterVertically
) {
    // Back button with 48dp touch target
    IconButton(
        onClick = { navController.navigateUp() },
        modifier = Modifier.size(48.dp)
    ) { ... }

    // Title with proper spacing
    Text(
        text = "Screen Title",
        modifier = Modifier
            .weight(1f)
            .padding(start = 8.dp) // UI.md title padding
    )

    // Single primary action (Save)
    StandardButton(onClick = { ... }) {
        Icon(...)
        Text("Save")
    }
}
```

**Content Area Action Buttons:**

```kotlin
// Action buttons positioned above main content
Row(
    modifier = Modifier
        .fillMaxWidth()
        .padding(bottom = 16.dp),
    horizontalArrangement = Arrangement.spacedBy(12.dp)
) {
    StandardButton(
        onClick = { ... },
        modifier = Modifier.weight(1f)
    ) { Text("Assign") }

    StandardButton(
        onClick = { ... },
        modifier = Modifier.weight(1f)
    ) { Text("Volunteers") }
}
```

**Usage Guidelines:**

- **Clean Header**: Keep header minimal with only essential actions (Save/Done)
- **Content Actions**: Place secondary actions in content area for better UX
- **Equal Weight**: Use weight(1f) for balanced button layout
- **Proper Spacing**: 12dp spacing between action buttons, 16dp bottom margin
- **Concise Labels**: Use short, clear text labels without icons for cleaner appearance
- **StandardButton**: Maintain consistent styling throughout

### Animation Integration Pattern

**Schedule Creation Screen Animation Sequence:**

```kotlin
// Animation state
var headerVisible by remember { mutableStateOf(false) }
var contentVisible by remember { mutableStateOf(false) }
var listVisible by remember { mutableStateOf(false) }

// UI.md timing pattern: 100ms → 200ms → 100ms
LaunchedEffect(Unit) {
    if (animationsEnabled) {
        delay(100); headerVisible = true
        delay(200); contentVisible = true
        delay(100); listVisible = true
    }
}

// Header animation
AnimatedVisibility(
    visible = headerVisible,
    enter = fadeIn(tween(500, delayMillis = 100))
)

// Content animation
AnimatedVisibility(
    visible = contentVisible,
    enter = slideInVertically(tween(400), { it / 4 }) +
            fadeIn(tween(300))
)

// List animation
AnimatedVisibility(
    visible = listVisible,
    enter = slideInVertically(tween(300), { it / 2 }) +
            fadeIn(tween(250))
)
```

### TFV Grid Layout Specifications

**Card Background Standards:**

- **Primary Background**: NeutralCardSurface (#1F1F1F) for all schedule tables
- **Corner Radius**: 16dp for main containers (following UI.md card specifications)
- **Spacing**: 16dp vertical spacing between school sections
- **Typography**: titleLarge for school headers, bodySmall for table headers

**Table Cell Specifications:**

- **Cell Height**: 52dp for improved touch targets and visual appeal
- **Cell Padding**: 6dp internal padding for better content spacing
- **Corner Radius**: 8dp for modern, rounded appearance
- **Touch Targets**: Full cell area clickable (52dp minimum)
- **Elevation**: 2dp default, 4dp when pressed for tactile feedback

**Enhanced TFV Cell Design:**

```kotlin
Card(
    modifier = modifier
        .height(52.dp)
        .clickable(onClick = onClick),
    colors = CardDefaults.cardColors(containerColor = backgroundColor),
    shape = RoundedCornerShape(8.dp),
    elevation = CardDefaults.cardElevation(
        defaultElevation = 2.dp,
        pressedElevation = 4.dp
    )
)
```

**TFV Color Coding System:**

- **Unassigned Slots**: Single yellow badge (#FFD600) on dark background (#1A1A1A) for all TFV values
- **Assigned Slots**: Rich green background (#2E7D32) with circular check mark (#4CAF50)

**Badge Styling:**

- **Corner Radius**: 12dp for TFV badges, circular for check marks
- **Typography**: titleMedium for TFV numbers, titleSmall for check marks
- **Padding**: 12dp horizontal, 6dp vertical for optimal readability
- **Shadow**: 1dp elevation for subtle depth effect

**Scrollability Improvements:**

- **Content Padding**: 100dp bottom padding to avoid bottom navigation bar overlap
- **LazyColumn**: Proper vertical spacing (16dp) between school sections
- **Full Scrollable Area**: All content accessible without obstruction

## Schedule Table Specifications (Teaching Slot Presets)

### Fixed Cell Dimensions Pattern

**Problem**: Variable cell sizing based on content and screen width creates inconsistent user experience and poor usability on different screen sizes.

**Solution**: Implement fixed cell dimensions with horizontal scrolling for consistent table layout.

#### Table Cell Specifications

**Fixed Dimensions:**

- **Cell Width**: 72dp (consistent across all slots)
- **Cell Height**: 60dp (maintains good touch targets)
- **Day Column Width**: 48dp (fixed, doesn't scroll)
- **Cell Spacing**: 8dp between cells
- **Corner Radius**: 8dp for modern appearance

**Responsive Background:**

- **Table Background**: Only extends to cover actual content (not full width)
- **Implementation**: Use `wrapContentWidth()` instead of `fillMaxWidth()` for table container and all rows
- **Consistent Header**: Header background uses unified Column structure with rounded corners for seamless appearance
- **Benefit**: Table background adapts to number of slots, preventing unnecessary empty space and visual cuts

**Implementation Pattern:**

```kotlin
// Shared scroll state for synchronized scrolling
val tableScrollState = rememberScrollState()

// Table card with responsive background
Card(
    modifier = Modifier
        .wrapContentWidth() // Only take up space needed for content
        .shadow(elevation = 4.dp, shape = RoundedCornerShape(16.dp)),
    shape = RoundedCornerShape(16.dp),
    colors = CardDefaults.cardColors(containerColor = animatedCardColor)
) {
    // Table content with unified background structure
    Column(
        modifier = Modifier
            .wrapContentWidth()
            .background(
                YellowAccent.copy(alpha = 0.15f),
                shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
            )
    ) {
        // Header with synchronized horizontal scrolling
        Row(
            modifier = Modifier
                .wrapContentWidth() // Only take up space needed for content
                .padding(vertical = 12.dp, horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
        // Fixed day column header (doesn't scroll)
        Text(
            text = "Day",
            modifier = Modifier
                .width(48.dp)
                .padding(horizontal = 4.dp),
            style = MaterialTheme.typography.titleSmall,
            fontWeight = FontWeight.Bold,
            color = YellowAccent
        )

        // Horizontally scrollable slot headers using shared scroll state
        Row(
            modifier = Modifier
                .wrapContentWidth() // Only take up space needed for content
                .horizontalScroll(tableScrollState), // Shared scroll state
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
        columnNames.forEachIndexed { index, columnName ->
            Column(
                modifier = Modifier.width(72.dp), // Fixed width
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Header content
            }
        }
    }

        // Table rows with synchronized scroll behavior
        Column(
            modifier = Modifier
                .wrapContentWidth() // Only take up space needed for content
                .background(
                    animatedCardColor,
                    shape = RoundedCornerShape(bottomStart = 16.dp, bottomEnd = 16.dp)
                )
                .padding(8.dp)
        ) {
        teachingSchedule.forEachIndexed { dayIndex, daySlots ->
            Row(
                modifier = Modifier
                    .wrapContentWidth() // Only take up space needed for content
                    .padding(vertical = 4.dp, horizontal = 4.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Fixed day name column (doesn't scroll)
                Text(
                    text = daySlots.day,
                    modifier = Modifier
                        .width(48.dp) // Match header width
                        .padding(horizontal = 4.dp),
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )

                // Horizontally scrollable slot cells using shared scroll state
                Row(
                    modifier = Modifier
                        .wrapContentWidth() // Only take up space needed for content
                        .horizontalScroll(tableScrollState), // Same shared scroll state
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
            daySlots.slots.forEachIndexed { slotIndex, slot ->
                Card(
                    modifier = Modifier
                        .width(80.dp) // Fixed width
                        .height(60.dp) // Fixed height
                        .clickable { /* slot action */ },
                    shape = RoundedCornerShape(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = if (isActive) YellowAccent else DarkGray
                    ),
                    elevation = CardDefaults.cardElevation(
                        defaultElevation = if (isActive) 2.dp else 0.dp
                    )
                ) {
                    // Cell content
                }
            }
        }
    }
        }
    }
}

// Bottom padding outside the table
Spacer(modifier = Modifier.height(100.dp))
```

#### Scrolling Behavior Specifications

**Synchronized Horizontal Scrolling:**

- **Scope**: Entire table content (header + all rows) scrolls together as one unit
- **Fixed Elements**: Day column remains fixed during horizontal scroll
- **Shared Scroll State**: Single `rememberScrollState()` shared between header and all rows for synchronized scrolling
- **Overflow Handling**: When time slots exceed screen width, horizontal scrolling activates automatically

**Bottom Padding:**

- **Padding Value**: 100dp bottom padding outside the table container
- **Purpose**: Prevents overlap with bottom navigation bar
- **Implementation**: Applied as `Spacer(modifier = Modifier.height(100.dp))` after main content area

#### Visual Design Standards

**Active Cells:**

- **Background**: YellowAccent (#FFD600)
- **Icon**: Close icon (18dp, black color)
- **Elevation**: 2dp for tactile feedback

**Inactive Cells:**

- **Background**: DarkGray
- **Content**: Empty (no icon)
- **Elevation**: 0dp (flat appearance)

**Touch Targets:**

- **Minimum Size**: 60dp height ensures accessibility compliance
- **Full Cell Clickable**: Entire 72dp × 60dp area is interactive
- **Visual Feedback**: Elevation change on active state

#### Usage Guidelines

**When to Apply:**

- Teaching Slot Preset creation/editing screens
- Any schedule table with variable column counts
- Tables that need consistent cell sizing regardless of content

**Benefits:**

- **Consistent UX**: Same cell size regardless of content or screen size
- **Better Usability**: Predictable touch targets and visual layout
- **Responsive Design**: Horizontal scrolling handles overflow gracefully
- **Responsive Background**: Table background adapts to content size, preventing unnecessary empty space
- **Accessibility**: Fixed 60dp height meets minimum touch target requirements

**Implementation Requirements:**

- Import `rememberScrollState` and `horizontalScroll` from Compose Foundation
- Apply fixed dimensions to both header and row cells
- Ensure day column width matches between header and rows
- Add sufficient bottom padding to prevent navigation bar overlap

---

**Last Updated**: Based on analysis of CreateTeachingSlotsScreen.kt, EditTeachingSlotsScreen.kt, TeachingSlotsScreen.kt, ScheduleMakerScreen.kt, TeachingSlotsOptionsScreen.kt, SetAvailabilityScreen.kt, and ScheduleCreationScreen.kt implementations. Includes robust animation reset patterns for navigation consistency, student selection interface specifications, schedule creation screen multi-action header patterns, and standardized schedule table specifications with fixed cell dimensions and horizontal scrolling.
