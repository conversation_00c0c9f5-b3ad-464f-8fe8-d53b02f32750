package com.phad.chatapp.utils

import android.content.Context
import android.content.SharedPreferences
import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore

/**
 * Session management for user login state
 * Stores login information in SharedPreferences and integrates with Firebase Auth
 */
class SessionManager(context: Context) {
    private val TAG = "SessionManager"
    
    // Shared preferences file name
    private val PREF_NAME = "ChatAppSession"
    
    // SharedPreferences and Editor
    private val pref: SharedPreferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE)
    private val editor: SharedPreferences.Editor = pref.edit()
    
    // Public variables
    companion object {
        // Shared preferences keys
        const val KEY_IS_LOGGED_IN = "isLoggedIn"
        const val KEY_USER_TYPE = "userType"
        const val KEY_USER_ROLL_NUMBER = "userRollNumber"
        const val KEY_USER_YEAR = "userYear"
        const val KEY_USER_NAME = "userName"
        const val KEY_BYPASS_FIREBASE_AUTH = "bypassFirebaseAuth"
    }
    
    /**
     * Create login session with user information
     */
    fun createLoginSession(userType: String, userRollNumber: String, userYear: Int) {
        Log.d(TAG, "Creating login session for: userType=$userType, userRoll=$userRollNumber, userYear=$userYear")
        
        // Store login state and user info
        editor.putBoolean(KEY_IS_LOGGED_IN, true)
        editor.putString(KEY_USER_TYPE, userType)
        editor.putString(KEY_USER_ROLL_NUMBER, userRollNumber)
        editor.putInt(KEY_USER_YEAR, userYear)
        
        // Commit changes
        editor.apply()
        
        // Verify data was stored correctly
        val savedUserType = pref.getString(KEY_USER_TYPE, null)
        Log.d(TAG, "Verified saved data: userType=$savedUserType")
        
        // Test Firebase authentication state
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        if (firebaseUser != null) {
            Log.d(TAG, "Firebase user is authenticated: ${firebaseUser.email}")
            
            // Test if we can access Firestore with this authentication
            testFirestoreAccess()
        } else {
            Log.w(TAG, "Firebase user is NOT authenticated!")
        }
    }
    
    /**
     * Emergency bypass for Firebase authentication issues
     * This should ONLY be used for testing/debugging
     */
    fun enableFirebaseAuthBypass(enable: Boolean) {
        Log.w(TAG, "Setting Firebase auth bypass to: $enable")
        editor.putBoolean(KEY_BYPASS_FIREBASE_AUTH, enable)
        editor.apply()
    }
    
    /**
     * Test if we can access Firestore with the current authentication
     */
    private fun testFirestoreAccess() {
        try {
            val db = FirebaseFirestore.getInstance()
            val testData = mapOf(
                "last_login" to com.google.firebase.Timestamp.now(),
                "test" to "Testing auth access",
                "user_id" to fetchUserId()
            )
            
            db.collection("test_connection")
                .document("auth_test")
                .set(testData)
                .addOnSuccessListener {
                    Log.d(TAG, "Successfully wrote to Firestore from SessionManager")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to write to Firestore from SessionManager", e)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error testing Firestore access from SessionManager", e)
        }
    }
    
    /**
     * Save user name to shared preferences
     */
    fun saveUserName(userName: String) {
        editor.putString(KEY_USER_NAME, userName)
        editor.apply()
        Log.d(TAG, "Saved user name: $userName")
    }
    
    /**
     * Fetch user name from shared preferences
     */
    fun fetchUserName(): String {
        return pref.getString(KEY_USER_NAME, "") ?: ""
    }
    
    /**
     * Get stored session data
     */
    fun getUserDetails(): HashMap<String, Any?> {
        val user = HashMap<String, Any?>()
        
        // Get stored user info
        val userType = pref.getString(KEY_USER_TYPE, null)
        val userRoll = pref.getString(KEY_USER_ROLL_NUMBER, null)
        val userYear = pref.getInt(KEY_USER_YEAR, 0)
        val userName = pref.getString(KEY_USER_NAME, null)
        
        user[KEY_USER_TYPE] = userType
        user[KEY_USER_ROLL_NUMBER] = userRoll
        user[KEY_USER_YEAR] = userYear
        user[KEY_USER_NAME] = userName
        
        Log.d(TAG, "Retrieved user details: userType=$userType, userRoll=$userRoll, userYear=$userYear, userName=$userName")
        
        return user
    }
    
    /**
     * Fetch the user's ID (roll number)
     */
    fun fetchUserId(): String {
        return pref.getString(KEY_USER_ROLL_NUMBER, "") ?: ""
    }
    
    /**
     * Fetch user type
     */
    fun fetchUserType(): String {
        return pref.getString(KEY_USER_TYPE, "") ?: ""
    }
    
    /**
     * Fetch user roll number
     */
    fun fetchRollNumber(): String? {
        return pref.getString(KEY_USER_ROLL_NUMBER, null)
    }
    
    /**
     * Check login status
     */
    fun isLoggedIn(): Boolean {
        // Get bypass setting
        val bypassAuth = pref.getBoolean(KEY_BYPASS_FIREBASE_AUTH, false)
        if (bypassAuth) {
            Log.w(TAG, "⚠️ USING AUTHENTICATION BYPASS! This is for testing only!")
            return pref.getBoolean(KEY_IS_LOGGED_IN, false)
        }
        
        // Check if user is logged in both in SharedPreferences and in Firebase Auth
        val isLoggedInPref = pref.getBoolean(KEY_IS_LOGGED_IN, false)
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        val isLoggedInFirebase = firebaseUser != null
        
        // Log detailed information about auth state
        if (isLoggedInFirebase && firebaseUser != null) {
            Log.d(TAG, "Firebase auth state: User=${firebaseUser.email}, UID=${firebaseUser.uid}")
            
            // Check if token is expired 
            firebaseUser.getIdToken(false)
                .addOnSuccessListener { result ->
                    val validToken = result.token != null
                    Log.d(TAG, "Token check: valid=$validToken")
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Failed to get ID token", e)
                }
        } else {
            Log.d(TAG, "Firebase auth state: Not authenticated")
        }
        
        Log.d(TAG, "Login status: SharedPrefs=$isLoggedInPref, Firebase=$isLoggedInFirebase")
        
        // Both must be true for the user to be considered logged in
        return isLoggedInPref && isLoggedInFirebase
    }
    
    /**
     * Clear session details
     */
    fun logoutUser() {
        Log.d(TAG, "Logging out user")
        
        // Sign out from Firebase Auth
        FirebaseAuth.getInstance().signOut()
        
        // Clear all data from SharedPreferences
        editor.clear()
        editor.apply()
    }
} 