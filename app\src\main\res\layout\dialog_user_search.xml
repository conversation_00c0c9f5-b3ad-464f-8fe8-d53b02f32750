<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="#0D0302"
    android:padding="16dp">

    <!-- Search Input -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:background="@drawable/bg_search_input"
        android:padding="8dp">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:src="@drawable/ic_search"
            android:tint="#AAAAAA"
            android:layout_marginEnd="8dp" />

        <EditText
            android:id="@+id/edit_search"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@android:color/transparent"
            android:hint="Search users..."
            android:textColorHint="#AAAAAA"
            android:textColor="#FFFFFF"
            android:inputType="text"
            android:maxLines="1"
            android:imeOptions="actionSearch" />

        <ImageButton
            android:id="@+id/btn_clear_search"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:src="@android:drawable/ic_menu_close_clear_cancel"
            android:tint="#AAAAAA"
            android:visibility="gone" />
    </LinearLayout>

    <!-- Loading Progress -->
    <ProgressBar
        android:id="@+id/progress_search"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="16dp"
        android:visibility="gone" />

    <!-- Search Results -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_search_results"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:maxHeight="350dp"
        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
        tools:listitem="@layout/item_search_result"
        tools:itemCount="3" />

    <!-- No Results Message -->
    <TextView
        android:id="@+id/text_no_results"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center"
        android:text="No users found"
        android:textColor="#AAAAAA"
        android:textSize="16sp"
        android:visibility="gone" />
</LinearLayout> 