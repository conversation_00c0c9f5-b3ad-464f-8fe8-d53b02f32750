<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- Author info -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/author_image"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:src="@drawable/default_profile_image"
                app:civ_border_width="1dp"
                app:civ_border_color="#EEEEEE"/>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical"
                android:layout_marginStart="12dp">

                <TextView
                    android:id="@+id/author_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textStyle="bold"
                    android:textSize="16sp"
                    android:textColor="?attr/colorOnSurface"
                    tools:text="John Doe"/>

                <TextView
                    android:id="@+id/timestamp"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textSize="12sp"
                    android:textColor="?attr/colorOnSurface"
                    android:alpha="0.6"
                    tools:text="2 hours ago"/>
            </LinearLayout>
        </LinearLayout>

        <!-- Content -->
        <TextView
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:textSize="14sp"
            android:textColor="?attr/colorOnSurface"
            tools:text="This is a sample update content with some text that might span multiple lines to show how it looks in the actual UI."/>

        <!-- Media Image (if any) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/media_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:id="@+id/media_image"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:scaleType="centerCrop"
                tools:src="@drawable/default_profile_image"/>
        </androidx.cardview.widget.CardView>

        <!-- Document (if any) -->
        <androidx.cardview.widget.CardView
            android:id="@+id/document_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            app:cardCornerRadius="8dp"
            app:cardBackgroundColor="#F5F5F5"
            android:visibility="gone"
            tools:visibility="visible">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:padding="12dp"
                android:gravity="center_vertical">

                <ImageView
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:src="@android:drawable/ic_menu_agenda"
                    app:tint="#757575"/>

                <TextView
                    android:id="@+id/document_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_marginStart="12dp"
                    android:textSize="14sp"
                    android:ellipsize="middle"
                    android:singleLine="true"
                    tools:text="document_name.pdf"/>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/download_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Open"
                    android:textSize="12sp"
                    app:icon="@android:drawable/ic_menu_view"
                    app:iconSize="16dp"
                    style="@style/Widget.MaterialComponents.Button.TextButton"/>
            </LinearLayout>
        </androidx.cardview.widget.CardView>
    </LinearLayout>
</androidx.cardview.widget.CardView> 