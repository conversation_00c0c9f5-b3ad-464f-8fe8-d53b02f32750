<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.CreateUpdateActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:title="Create Update"
            app:titleTextColor="@android:color/white" />

    </com.google.android.material.appbar.AppBarLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/appBarLayout"
        app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Title Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/updateTitleEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Title (optional)"
                    android:inputType="textCapSentences" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Category Selection -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">

                <AutoCompleteTextView
                    android:id="@+id/categoryDropdown"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Category"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Important Flag -->
            <CheckBox
                android:id="@+id/importantCheckbox"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Mark as Important"
                android:layout_marginBottom="16dp" />

            <!-- Content Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/updateContentEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:minHeight="120dp"
                    android:gravity="top|start"
                    android:hint="What would you like to share?"
                    android:inputType="textMultiLine" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Event Date (for event category) -->
            <LinearLayout
                android:id="@+id/eventDateContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Event Date"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/eventDateButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Select Date"
                    app:icon="@android:drawable/ic_menu_my_calendar"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />
            </LinearLayout>

            <!-- Location Input -->
            <LinearLayout
                android:id="@+id/locationContainer"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Location (optional)"
                    android:textStyle="bold"
                    android:layout_marginBottom="4dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/locationNameEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Location Name (e.g. Main Auditorium)"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/locationAddressEditText"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:hint="Address (optional)"
                        android:inputType="textPostalAddress" />

                </com.google.android.material.textfield.TextInputLayout>

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/pickLocationButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:text="Pick Location on Map"
                    app:icon="@android:drawable/ic_dialog_map"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />
            </LinearLayout>

            <!-- Tags Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/tagsEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="Tags (comma separated, e.g. urgent, workshop)"
                    android:inputType="text" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- External Link Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/externalLinkEditText"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="External Link (optional)"
                    android:inputType="textUri" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Attachment Buttons -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/addImageButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="8dp"
                    android:text="Add Image"
                    app:icon="@android:drawable/ic_menu_camera"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/addDocumentButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Add Document"
                    app:icon="@android:drawable/ic_menu_agenda"
                    style="@style/Widget.MaterialComponents.Button.OutlinedButton" />

            </LinearLayout>

            <!-- Selected Image Preview -->
            <androidx.cardview.widget.CardView
                android:id="@+id/selectedImageLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                android:visibility="gone"
                tools:visibility="visible">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <ImageView
                        android:id="@+id/selectedImage"
                        android:layout_width="match_parent"
                        android:layout_height="200dp"
                        android:scaleType="centerCrop"
                        tools:src="@android:drawable/ic_menu_gallery" />

                    <ImageButton
                        android:id="@+id/removeImageButton"
                        android:layout_width="36dp"
                        android:layout_height="36dp"
                        android:layout_gravity="top|end"
                        android:layout_margin="8dp"
                        android:src="@android:drawable/ic_menu_close_clear_cancel"
                        android:background="@drawable/circular_background"
                        android:contentDescription="Remove image" />
                </FrameLayout>
            </androidx.cardview.widget.CardView>

            <!-- Selected Document Preview -->
            <androidx.cardview.widget.CardView
                android:id="@+id/selectedDocumentLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="#F5F5F5"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="16dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_agenda"
                        app:tint="#757575"/>

                    <TextView
                        android:id="@+id/documentNameText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:ellipsize="middle"
                        android:singleLine="true"
                        tools:text="document_name.pdf"/>

                    <ImageButton
                        android:id="@+id/removeDocumentButton"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_close_clear_cancel"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:contentDescription="Remove document" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Post Button -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp">

                <com.google.android.material.button.MaterialButton
                    android:id="@+id/postUpdateButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="Post Update"
                    android:paddingTop="12dp"
                    android:paddingBottom="12dp"
                    app:cornerRadius="8dp" />

                <ProgressBar
                    android:id="@+id/progressBar"
                    android:layout_width="36dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center"
                    android:visibility="gone" />
            </FrameLayout>
        </LinearLayout>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout> 