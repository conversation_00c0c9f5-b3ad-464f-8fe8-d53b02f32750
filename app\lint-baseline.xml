<?xml version="1.0" encoding="UTF-8"?>
<issues format="6" by="lint 8.9.0" type="baseline" client="gradle" dependencies="false" name="AGP (8.9.0)" variant="all" version="8.9.0">

    <issue
        id="DefaultLocale"
        message="Implicitly using the default locale is a common source of bugs: Use `toUpperCase(Locale)` instead. For strings meant to be internal use `Locale.ROOT`, otherwise `Locale.getDefault()`."
        errorLine1="        val normalizedUserType = when (userType.toUpperCase()) {"
        errorLine2="                                                ~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/LoginActivity.kt"
            line="131"
            column="49"/>
    </issue>

    <issue
        id="InternalInsetResource"
        message="Using internal inset dimension resource `status_bar_height` is not supported"
        errorLine1="        val statusBarHeight = resources.getIdentifier("
        errorLine2="                              ^">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="56"
            column="31"/>
    </issue>

    <issue
        id="InflateParams"
        message="Avoid passing `null` as the view root (needed to resolve layout parameters on the inflated layout&apos;s root element)"
        errorLine1="        val view = inflater.inflate(R.layout.popup_menu_admin, null)"
        errorLine2="                                                               ~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="288"
            column="64"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-bom than 32.5.0 is available: 33.11.0"
        errorLine1="    implementation(platform(&quot;com.google.firebase:firebase-bom:32.5.0&quot;))"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-firestore than 25.1.2 is available: 25.1.3"
        errorLine1="firebaseFirestore = &quot;25.1.2&quot;"
        errorLine2="                    ~~~~~~~~">
        <location
            file="$HOME/AndroidStudioProjects/ChatApp/gradle/libs.versions.toml"
            line="13"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-firestore than 25.1.2 is available: 25.1.3"
        errorLine1="firebaseFirestore = &quot;25.1.2&quot;"
        errorLine2="                    ~~~~~~~~">
        <location
            file="$HOME/AndroidStudioProjects/ChatApp/gradle/libs.versions.toml"
            line="13"
            column="21"/>
    </issue>

    <issue
        id="GradleDependency"
        message="A newer version of com.google.firebase:firebase-firestore than 25.1.2 is available: 25.1.3"
        errorLine1="firebaseFirestore = &quot;25.1.2&quot;"
        errorLine2="                    ~~~~~~~~">
        <location
            file="$HOME/AndroidStudioProjects/ChatApp/gradle/libs.versions.toml"
            line="13"
            column="21"/>
    </issue>

    <issue
        id="DiscouragedApi"
        message="Use of this function is discouraged because resource reflection makes it harder to perform build optimizations and compile-time verification of code. It is much more efficient to retrieve resources by identifier (e.g. `R.foo.bar`) than by name (e.g. `getIdentifier(&quot;bar&quot;, &quot;foo&quot;, null)`)."
        errorLine1="        val statusBarHeight = resources.getIdentifier("
        errorLine2="                                        ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="56"
            column="41"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="                android:tint=&quot;@color/white&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="102"
            column="17"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/white&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_community_tab.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/whatsapp_light_green&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UseAppTint"
        message="Must use `app:tint` instead of `android:tint`"
        errorLine1="            android:tint=&quot;@color/white&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="Typos"
        message="Did you mean &quot;Feature!&quot; instead of &quot;Feature1&quot;?"
        errorLine1="    &lt;string name=&quot;feature1&quot;>Feature1&lt;/string>"
        errorLine2="                            ^">
        <location
            file="src/main/res/values/strings.xml"
            line="4"
            column="29"/>
    </issue>

    <issue
        id="Typos"
        message="Did you mean &quot;Admin!&quot; instead of &quot;Admin1&quot;?"
        errorLine1="    &lt;string name=&quot;admin1&quot;>Admin1&lt;/string>"
        errorLine2="                          ^">
        <location
            file="src/main/res/values/strings.xml"
            line="26"
            column="27"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/AdminAdapter.kt"
            line="26"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/ChatMessageAdapter.kt"
            line="53"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/CommunitiesAdapter.kt"
            line="25"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/GroupAdapter.kt"
            line="48"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="                        notifyDataSetChanged()"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/MessageAdapter.kt"
            line="119"
            column="25"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/ParticipantAdapter.kt"
            line="35"
            column="9"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="            notifyDataSetChanged()"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/ParticipantAdapter.kt"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="NotifyDataSetChanged"
        message="It will always be more efficient to use more specific change events if you can. Rely on `notifyDataSetChanged` as a last resort."
        errorLine1="        notifyDataSetChanged()"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/UserAvatarAdapter.kt"
            line="28"
            column="9"/>
    </issue>

    <issue
        id="StaticFieldLeak"
        message="Do not place Android context classes in static fields (static reference to `FirebaseFirestore` which has field `context` pointing to `Context`); this is a memory leak"
        errorLine1="    private val firestore = FirebaseFirestore.getInstance()"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/utils/FirestoreSetup.kt"
            line="19"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/teal` with a theme that also paints a background (inferred theme is `@style/Theme_ChatApp`)"
        errorLine1="    android:background=&quot;@color/teal&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#121212` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;#121212&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="8"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@color/black&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#000000` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;#000000&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@android:color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@android:color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_admin_list.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chat.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@android:color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@android:color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chats_tab.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@color/black&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_community_tab.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `@color/black` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;@color/black&quot;"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_remove_group.xml"
            line="7"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `?attr/selectableItemBackground` with a theme that also paints a background (inferred theme is `@style/Theme.ChatApp`)"
        errorLine1="    android:background=&quot;?attr/selectableItemBackground&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_participant.xml"
            line="9"
            column="5"/>
    </issue>

    <issue
        id="Overdraw"
        message="Possible overdraw: Root element paints background `#1F1F1F` with a theme that also paints a background (inferred theme is `@style/Theme_ChatApp`)"
        errorLine1="    android:background=&quot;#1F1F1F&quot;>"
        errorLine2="    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/popup_menu_admin.xml"
            line="6"
            column="5"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.bg_message_received` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/bg_message_received.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.bg_message_sent` appears to be unused"
        errorLine1="&lt;shape xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/bg_message_sent.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.menu.chat_menu` appears to be unused"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/menu/chat_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_200` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_200&quot;>#FFBB86FC&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_500` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_500&quot;>#FF6200EE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="4"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.purple_700` appears to be unused"
        errorLine1="    &lt;color name=&quot;purple_700&quot;>#FF3700B3&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="5"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.primary_chat_app` appears to be unused"
        errorLine1="    &lt;color name=&quot;primary_chat_app&quot;>#1F9AD6&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="11"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_primary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_primary&quot;>#EEEEEE&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="12"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.background_secondary` appears to be unused"
        errorLine1="    &lt;color name=&quot;background_secondary&quot;>#FFFFFF&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="13"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.whatsapp_dark_gray` appears to be unused"
        errorLine1="    &lt;color name=&quot;whatsapp_dark_gray&quot;>#303030&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="19"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.whatsapp_light_gray` appears to be unused"
        errorLine1="    &lt;color name=&quot;whatsapp_light_gray&quot;>#F0F0F0&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="20"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.whatsapp_chat_background` appears to be unused"
        errorLine1="    &lt;color name=&quot;whatsapp_chat_background&quot;>#E5DDD5&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="21"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorPrimary` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorPrimary&quot;>#008577&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="24"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorPrimaryDark` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorPrimaryDark&quot;>#00574B&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="25"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.color.colorAccent` appears to be unused"
        errorLine1="    &lt;color name=&quot;colorAccent&quot;>#D81B60&lt;/color>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/colors.xml"
            line="26"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.default_group_avatar` appears to be unused"
        errorLine1="&lt;layer-list xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/drawable/default_group_avatar.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.fragment_add_group_fallback` appears to be unused"
        errorLine1="&lt;LinearLayout xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.fragment_groups_tab` appears to be unused"
        errorLine1="&lt;androidx.constraintlayout.widget.ConstraintLayout"
        errorLine2="^">
        <location
            file="src/main/res/layout/fragment_groups_tab.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_add` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_add.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.ic_group` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/ic_group.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.layout.item_chat` appears to be unused"
        errorLine1="&lt;androidx.constraintlayout.widget.ConstraintLayout"
        errorLine2="^">
        <location
            file="src/main/res/layout/item_chat.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.menu.popup_menu_admin` appears to be unused"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;>"
        errorLine2="^">
        <location
            file="src/main/res/menu/popup_menu_admin.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.chats` appears to be unused"
        errorLine1="    &lt;string name=&quot;chats&quot;>Chats&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="10"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.groups` appears to be unused"
        errorLine1="    &lt;string name=&quot;groups&quot;>Groups&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="11"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.community` appears to be unused"
        errorLine1="    &lt;string name=&quot;community&quot;>Community&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="12"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.search` appears to be unused"
        errorLine1="    &lt;string name=&quot;search&quot;>Search&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="13"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.more` appears to be unused"
        errorLine1="    &lt;string name=&quot;more&quot;>More&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="14"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.new_chat` appears to be unused"
        errorLine1="    &lt;string name=&quot;new_chat&quot;>New Chat&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="17"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.new_group` appears to be unused"
        errorLine1="    &lt;string name=&quot;new_group&quot;>New Group&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="18"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_chats_available` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_chats_available&quot;>No chats available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_groups_available` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_groups_available&quot;>No groups available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="20"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_communities_available` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_communities_available&quot;>No communities available&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="21"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.email` appears to be unused"
        errorLine1="    &lt;string name=&quot;email&quot;>Email&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.password` appears to be unused"
        errorLine1="    &lt;string name=&quot;password&quot;>Password&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="23"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login` appears to be unused"
        errorLine1="    &lt;string name=&quot;login&quot;>Login&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.submit` appears to be unused"
        errorLine1="    &lt;string name=&quot;submit&quot;>Submit&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="25"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.admin1` appears to be unused"
        errorLine1="    &lt;string name=&quot;admin1&quot;>Admin1&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="26"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.admin2` appears to be unused"
        errorLine1="    &lt;string name=&quot;admin2&quot;>Admin2&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="27"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.student` appears to be unused"
        errorLine1="    &lt;string name=&quot;student&quot;>Student&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.invalid_email` appears to be unused"
        errorLine1="    &lt;string name=&quot;invalid_email&quot;>Invalid Email Address&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.invalid_password` appears to be unused"
        errorLine1="    &lt;string name=&quot;invalid_password&quot;>Password must be at least 6 characters&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="30"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.account_created` appears to be unused"
        errorLine1="    &lt;string name=&quot;account_created&quot;>Account Created Successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="31"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_success` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_success&quot;>Login Successful&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="32"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.login_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;login_failed&quot;>Login Failed&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="33"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.error_occurred` appears to be unused"
        errorLine1="    &lt;string name=&quot;error_occurred&quot;>An error occurred&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="34"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.invalid_account` appears to be unused"
        errorLine1="    &lt;string name=&quot;invalid_account&quot;>Invalid account type or credentials&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.logout` appears to be unused"
        errorLine1="    &lt;string name=&quot;logout&quot;>Logout&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="36"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.profile` appears to be unused"
        errorLine1="    &lt;string name=&quot;profile&quot;>Profile&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.notification` appears to be unused"
        errorLine1="    &lt;string name=&quot;notification&quot;>Notification&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="38"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.settings` appears to be unused"
        errorLine1="    &lt;string name=&quot;settings&quot;>Settings&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.home` appears to be unused"
        errorLine1="    &lt;string name=&quot;home&quot;>Home&lt;/string>"
        errorLine2="            ~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="40"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.signup` appears to be unused"
        errorLine1="    &lt;string name=&quot;signup&quot;>Sign Up&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.already_have_account` appears to be unused"
        errorLine1="    &lt;string name=&quot;already_have_account&quot;>Already have an account? Login&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="42"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.create_group` appears to be unused"
        errorLine1="    &lt;string name=&quot;create_group&quot;>Create Group&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="44"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.group_name` appears to be unused"
        errorLine1="    &lt;string name=&quot;group_name&quot;>Group Name&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.group_description` appears to be unused"
        errorLine1="    &lt;string name=&quot;group_description&quot;>Group Description&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="46"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.create` appears to be unused"
        errorLine1="    &lt;string name=&quot;create&quot;>Create&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="47"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.add_group` appears to be unused"
        errorLine1="    &lt;string name=&quot;add_group&quot;>Add Group&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="48"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.remove_group` appears to be unused"
        errorLine1="    &lt;string name=&quot;remove_group&quot;>Remove Group&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.confirm_group_removal` appears to be unused"
        errorLine1="    &lt;string name=&quot;confirm_group_removal&quot;>Are you sure you want to remove this group?&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="50"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.yes` appears to be unused"
        errorLine1="    &lt;string name=&quot;yes&quot;>Yes&lt;/string>"
        errorLine2="            ~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="51"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no` appears to be unused"
        errorLine1="    &lt;string name=&quot;no&quot;>No&lt;/string>"
        errorLine2="            ~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="52"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.group_deleted` appears to be unused"
        errorLine1="    &lt;string name=&quot;group_deleted&quot;>Group deleted successfully&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="53"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.group_deletion_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;group_deletion_failed&quot;>Failed to delete group&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.no_groups_found` appears to be unused"
        errorLine1="    &lt;string name=&quot;no_groups_found&quot;>No groups found&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="55"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.send_message` appears to be unused"
        errorLine1="    &lt;string name=&quot;send_message&quot;>Send Message&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="56"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.type_message` appears to be unused"
        errorLine1="    &lt;string name=&quot;type_message&quot;>Type a message&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="57"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.message_sent` appears to be unused"
        errorLine1="    &lt;string name=&quot;message_sent&quot;>Message sent&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="58"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.message_failed` appears to be unused"
        errorLine1="    &lt;string name=&quot;message_failed&quot;>Failed to send message&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="59"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.string.group_chat` appears to be unused"
        errorLine1="    &lt;string name=&quot;group_chat&quot;>Group Chat&lt;/string>"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/strings.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.style.Base_Theme_ChatApp` appears to be unused"
        errorLine1="    &lt;style name=&quot;Base.Theme.ChatApp&quot; parent=&quot;Theme.Material3.DayNight.NoActionBar&quot;>"
        errorLine2="           ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/values/themes.xml"
            line="3"
            column="12"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.menu.whatsapp_menu` appears to be unused"
        errorLine1="&lt;menu xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/menu/whatsapp_menu.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="UnusedResources"
        message="The resource `R.drawable.whatsapp_pattern` appears to be unused"
        errorLine1="&lt;vector xmlns:android=&quot;http://schemas.android.com/apk/res/android&quot;"
        errorLine2="^">
        <location
            file="src/main/res/drawable/whatsapp_pattern.xml"
            line="2"
            column="1"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="        &lt;Button"
        errorLine2="         ~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="45"
            column="10"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="41"
            column="14"/>
    </issue>

    <issue
        id="ButtonStyle"
        message="Buttons in button bars should be borderless; use `style=&quot;?android:attr/buttonBarButtonStyle&quot;` (and `?android:attr/buttonBarStyle` on the parent)"
        errorLine1="            &lt;Button"
        errorLine2="             ~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="51"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        message="The view name (`@+id/edit_text_roll_number`) suggests this is a number, but it does not include a numeric `inputType` (such as &apos;`numberSigned`&apos;)"
        errorLine1="            android:inputType=&quot;text&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="55"
            column="13"/>
        <location
            file="src/main/res/layout/activity_login.xml"
            line="51"
            column="13"
            message="id defined here"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="78"
            column="14"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="25"
            column="10"/>
    </issue>

    <issue
        id="TextFields"
        message="This text field does not specify an `inputType`"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="26"
            column="6"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_chat.xml"
            line="54"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_group_chat.xml"
            line="108"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="50"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="75"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="102"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="129"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="            &lt;EditText"
        errorLine2="             ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="78"
            column="14"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="25"
            column="10"/>
    </issue>

    <issue
        id="Autofill"
        message="Missing `autofillHints` attribute"
        errorLine1="    &lt;EditText"
        errorLine2="     ~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="26"
            column="6"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="                    .setColor(if (isEveryone) Color.parseColor(&quot;#FFC107&quot;) else Color.parseColor(&quot;#4CAF50&quot;))"
        errorLine2="                                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/utils/ChatMessagingService.kt"
            line="202"
            column="47"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="                    .setColor(if (isEveryone) Color.parseColor(&quot;#FFC107&quot;) else Color.parseColor(&quot;#4CAF50&quot;))"
        errorLine2="                                                                               ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/utils/ChatMessagingService.kt"
            line="202"
            column="80"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="                    .setColor(Color.parseColor(&quot;#FF5252&quot;)) // Red for important"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/utils/ChatMessagingService.kt"
            line="352"
            column="31"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension property `Menu.size` instead?"
        errorLine1="            Log.d(TAG, &quot;Popup menu created with ${popupMenu.menu.size()} items for user type: &apos;$userType&apos;&quot;)"
        errorLine2="                                                  ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/CommunityTabFragment.kt"
            line="122"
            column="51"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="    private val EVERYONE_COLOR = Color.parseColor(&quot;#FFCA28&quot;) // Amber"
        errorLine2="                                 ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/MessageAdapter.kt"
            line="38"
            column="34"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="    private val MENTION_COLOR = Color.parseColor(&quot;#4CAF50&quot;) // Green"
        errorLine2="                                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/MessageAdapter.kt"
            line="39"
            column="33"/>
    </issue>

    <issue
        id="UseKtx"
        message="Use the KTX extension function `String.toColorInt` instead?"
        errorLine1="    private val IMPORTANT_COLOR = Color.parseColor(&quot;#FF5252&quot;) // Red"
        errorLine2="                                  ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/adapters/MessageAdapter.kt"
            line="40"
            column="35"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;de.hdodenhof:circleimageview:3.1.0&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="68"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(platform(&quot;com.google.firebase:firebase-bom:32.5.0&quot;))"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="71"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead (com.google.firebase:firebase-firestore is already available as `firebase-firestore`, but using version 25.1.2 instead)"
        errorLine1="    implementation(&quot;com.google.firebase:firebase-firestore&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="74"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.firebase:firebase-analytics&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="75"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.firebase:firebase-auth&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="76"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;com.google.firebase:firebase-messaging&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="77"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-play-services:1.7.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="86"
            column="20"/>
    </issue>

    <issue
        id="UseTomlInstead"
        message="Use version catalog instead"
        errorLine1="    implementation(&quot;org.jetbrains.kotlinx:kotlinx-coroutines-android:1.7.3&quot;)"
        errorLine2="                   ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="build.gradle.kts"
            line="87"
            column="20"/>
    </issue>

    <issue
        id="ContentDescription"
        message="Missing `contentDescription` attribute on image"
        errorLine1="    &lt;ImageView"
        errorLine2="     ~~~~~~~~~">
        <location
            file="src/main/res/layout/item_participant.xml"
            line="11"
            column="6"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="50"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="75"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="102"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="129"
            column="10"/>
    </issue>

    <issue
        id="LabelFor"
        message="Missing accessibility label: provide either a view with an `android:labelFor` that references this view or provide an `android:hint`"
        errorLine1="        &lt;EditText"
        errorLine2="         ~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="25"
            column="10"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                text = &quot;Error loading Add Group screen: ${e.message}\n\nTap to go back&quot;"
        errorLine2="                       ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/AddGroupFragment.kt"
            line="126"
            column="24"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                text = &quot;Error loading Add Group screen: ${e.message}\n\nTap to go back&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/AddGroupFragment.kt"
            line="126"
            column="25"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                text = &quot;Error loading Add Group screen: ${e.message}\n\nTap to go back&quot;"
        errorLine2="                                                                        ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/AddGroupFragment.kt"
            line="126"
            column="73"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="            &quot;Selected Participants ($selectedCount)&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/AddGroupFragment.kt"
            line="217"
            column="13"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            &quot;Selected Participants ($selectedCount)&quot;"
        errorLine2="             ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/AddGroupFragment.kt"
            line="217"
            column="14"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        textView.text = &quot;Coming Soon: $tabName&quot;"
        errorLine2="                        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ChatPagerAdapter.kt"
            line="57"
            column="25"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        textView.text = &quot;Coming Soon: $tabName&quot;"
        errorLine2="                         ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ChatPagerAdapter.kt"
            line="57"
            column="26"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    emptyView.text = &quot;No groups in community&quot; // Make sure text is set"
        errorLine2="                                      ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/CommunityTabFragment.kt"
            line="181"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="                        messageView.text = &quot;To enter the group, contact the admin:\n\nName: $creatorName\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="172"
            column="44"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                        messageView.text = &quot;To enter the group, contact the admin:\n\nName: $creatorName\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="172"
            column="45"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                        messageView.text = &quot;To enter the group, contact the admin:\n\nName: $creatorName\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                                                                                      ~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="172"
            column="87"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                        messageView.text = &quot;To enter the group, contact the admin:\n\nName: $creatorName\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                                                                                                          ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="172"
            column="107"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        messageView.text = &quot;To enter the group, contact the admin:\n\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="205"
            column="28"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        messageView.text = &quot;To enter the group, contact the admin:\n\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="205"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        messageView.text = &quot;To enter the group, contact the admin:\n\nRoll Number: $adminRollNumber&quot;"
        errorLine2="                                                                      ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="205"
            column="71"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        messageView.text = &quot;To enter the group, contact the admin with your roll number and name.&quot;"
        errorLine2="                            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="216"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        titleView.text = &quot;Edit Group Name&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="336"
            column="27"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        titleView.text = &quot;Edit Description&quot;"
        errorLine2="                          ~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="370"
            column="27"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        dialogBinding.groupParticipantsCount.text = &quot;${currentGroup?.participants?.size ?: 0} participants&quot;"
        errorLine2="                                                    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="484"
            column="53"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        dialogBinding.groupParticipantsCount.text = &quot;${currentGroup?.participants?.size ?: 0} participants&quot;"
        errorLine2="                                                                                             ~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupChatActivity.kt"
            line="484"
            column="94"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="            text = &quot;Groups Tab&quot;"
        errorLine2="                    ~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/GroupsTabFragment.kt"
            line="19"
            column="21"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        textViewStatus.text = &quot;Authenticating...&quot;"
        errorLine2="                               ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/LoginActivity.kt"
            line="128"
            column="32"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.toolbarTitle.text = &quot;Manage $groupName&quot;"
        errorLine2="                                    ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="69"
            column="37"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.toolbarTitle.text = &quot;Manage $groupName&quot;"
        errorLine2="                                     ~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="69"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="                    binding.allUsersLabel.text = &quot;No users found&quot;"
        errorLine2="                                                  ~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="192"
            column="51"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        binding.allUsersLabel.text = &quot;All Users (${count} selected)&quot;"
        errorLine2="                                     ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="213"
            column="38"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.allUsersLabel.text = &quot;All Users (${count} selected)&quot;"
        errorLine2="                                      ~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="213"
            column="39"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        binding.allUsersLabel.text = &quot;All Users (${count} selected)&quot;"
        errorLine2="                                                         ~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/ManageParticipantsActivity.kt"
            line="213"
            column="58"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        dialogTitle.text = &quot;Delete Group&quot;"
        errorLine2="                            ~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/RemoveGroupFragment.kt"
            line="125"
            column="29"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="Do not concatenate text displayed with `setText`. Use resource string with placeholders."
        errorLine1="        dialogMessage.text = &quot;Are you sure you want to delete the group &apos;${group.name}&apos;? This action cannot be undone.&quot;"
        errorLine2="                             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/RemoveGroupFragment.kt"
            line="126"
            column="30"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        dialogMessage.text = &quot;Are you sure you want to delete the group &apos;${group.name}&apos;? This action cannot be undone.&quot;"
        errorLine2="                              ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/RemoveGroupFragment.kt"
            line="126"
            column="31"/>
    </issue>

    <issue
        id="SetTextI18n"
        message="String literal in `setText` can not be translated. Use Android resources instead."
        errorLine1="        dialogMessage.text = &quot;Are you sure you want to delete the group &apos;${group.name}&apos;? This action cannot be undone.&quot;"
        errorLine2="                                                                                      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/java/com/phad/chatapp/RemoveGroupFragment.kt"
            line="126"
            column="87"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Type a message&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Type a message&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_chat.xml"
            line="60"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Send message&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Send message&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_chat.xml"
            line="73"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Search&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Search&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_group_chat.xml"
            line="60"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Menu&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Menu&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_group_chat.xml"
            line="73"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Write Message&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Write Message&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_group_chat.xml"
            line="113"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Send message&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Send message&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_group_chat.xml"
            line="134"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Login&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Login&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Roll Number&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Roll Number&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="42"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Email&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Email&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="67"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Password&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Password&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="92"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Passkey&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Passkey&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="119"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Sign In&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Sign In&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_login.xml"
            line="149"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Feature Content&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Feature Content&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_main.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Back&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Back&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="37"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Manage Participants&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Manage Participants&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Search users&quot;, should use `@string` resource"
        errorLine1="                android:hint=&quot;Search users&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="88"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;All Users (Selected users are in the group)&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;All Users (Selected users are in the group)&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="104"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;SAVE CHANGES&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;SAVE CHANGES&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/activity_manage_participants.xml"
            line="137"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Search&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Search&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/chat_menu.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Refresh&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Refresh&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/chat_menu.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;More&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;More&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/chat_menu.xml"
            line="17"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Reload Users&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Reload Users&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/chats_menu.xml"
            line="8"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Force Admin1 Mode&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Force Admin1 Mode&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/chats_menu.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Group&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Add Group&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/community_menu.xml"
            line="6"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Remove Group&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Remove Group&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/community_menu.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Logout&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Logout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/community_menu.xml"
            line="16"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Access Denied&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Access Denied&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_access_denied.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;To enter the group, contact the admin with your roll number and name.&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;To enter the group, contact the admin with your roll number and name.&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_access_denied.xml"
            line="29"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;OK&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;OK&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_access_denied.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Delete Group&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Delete Group&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="14"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Are you sure you want to delete this group? This action cannot be undone.&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Are you sure you want to delete this group? This action cannot be undone.&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="24"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Cancel&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="39"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Delete&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Delete&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_delete_confirmation.xml"
            line="49"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Edit Group Name&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Edit Group Name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Cancel&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Cancel&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="46"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Save&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Save&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_edit_text.xml"
            line="56"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group Info&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Group Info&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Description&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="28"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group description goes here&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Group description goes here&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="37"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Created by&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Created by&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="45"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Creator name&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Creator name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="54"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Created on&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Created on&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="62"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;January 1, 2023&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;January 1, 2023&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="71"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Participants&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Participants&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="79"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;5 participants&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;5 participants&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="88"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Close&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Close&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/dialog_group_info.xml"
            line="96"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group Name&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Group Name&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="41"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group Description&quot;, should use `@string` resource"
        errorLine1="            android:hint=&quot;Group Description&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Select Participants&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Select Participants&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="93"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add participants&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Add participants&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="103"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Selected Participants (0)&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Selected Participants (0)&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="111"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Create Group&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Create Group&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group.xml"
            line="129"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Add Group&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Add Group&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="19"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Enter Group Name&quot;, should use `@string` resource"
        errorLine1="        android:hint=&quot;Enter Group Name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="31"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Participants&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Participants&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="41"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Create Group&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Create Group&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_add_group_fallback.xml"
            line="57"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Messages&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Messages&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chat.xml"
            line="29"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Search&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Search&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chat.xml"
            line="42"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Settings&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;Settings&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chat.xml"
            line="53"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Communities&quot;, should use `@string` resource"
        errorLine1="                    android:text=&quot;Communities&quot;"
        errorLine2="                    ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chat.xml"
            line="103"
            column="21"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No admins found&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No admins found&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_chats_tab.xml"
            line="45"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Community&quot;, should use `@string` resource"
        errorLine1="            android:text=&quot;Community&quot;"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_community_tab.xml"
            line="22"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Menu&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Menu&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_community_tab.xml"
            line="35"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No groups in community&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No groups in community&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_community_tab.xml"
            line="43"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No groups available&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No groups available&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_groups_tab.xml"
            line="21"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Select a group to remove&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Select a group to remove&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_remove_group.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;No groups available&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;No groups available&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/fragment_remove_group.xml"
            line="50"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Admin_Name&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Admin_Name&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_admin.xml"
            line="41"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Description&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Description&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_admin.xml"
            line="51"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Profile picture&quot;, should use `@string` resource"
        errorLine1="        android:contentDescription=&quot;Profile picture&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_chat.xml"
            line="17"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Group&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="24"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group Name&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Group Name&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="37"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Group description&quot;, should use `@string` resource"
        errorLine1="                android:text=&quot;Group description&quot;"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="46"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Delete&quot;, should use `@string` resource"
        errorLine1="            android:contentDescription=&quot;Delete&quot; />"
        errorLine2="            ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_group.xml"
            line="61"
            column="13"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;User avatar&quot;, should use `@string` resource"
        errorLine1="                android:contentDescription=&quot;User avatar&quot; />"
        errorLine2="                ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/item_user_avatar.xml"
            line="28"
            column="17"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Logout&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Logout&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/main_menu.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Manage Participants&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Manage Participants&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/popup_menu_admin.xml"
            line="5"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Edit Group Name&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Edit Group Name&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/popup_menu_admin.xml"
            line="8"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Edit Description&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Edit Description&quot; />"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/popup_menu_admin.xml"
            line="11"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Manage Participants&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Manage Participants&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/popup_menu_admin.xml"
            line="12"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Edit Group Name&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Edit Group Name&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/popup_menu_admin.xml"
            line="32"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Edit Description&quot;, should use `@string` resource"
        errorLine1="        android:text=&quot;Edit Description&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/layout/popup_menu_admin.xml"
            line="52"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;Search&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;Search&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/whatsapp_menu.xml"
            line="7"
            column="9"/>
    </issue>

    <issue
        id="HardcodedText"
        message="Hardcoded string &quot;More&quot;, should use `@string` resource"
        errorLine1="        android:title=&quot;More&quot;"
        errorLine2="        ~~~~~~~~~~~~~~~~~~~~">
        <location
            file="src/main/res/menu/whatsapp_menu.xml"
            line="12"
            column="9"/>
    </issue>

</issues>
