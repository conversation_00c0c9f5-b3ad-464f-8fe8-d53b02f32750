<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0D0302"
    android:fitsSystemWindows="true">

    <!-- Toolbar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#0D0302"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/back_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />

            <TextView
                android:id="@+id/title_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="Manage Permissions"
                android:textColor="#FFFFFF"
                android:textSize="18sp"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/search_button"
                app:layout_constraintStart_toEndOf="@+id/back_button"
                app:layout_constraintTop_toTopOf="parent" />
                
            <ImageView
                android:id="@+id/search_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@android:drawable/ic_menu_search"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Search bar (initially hidden) -->
    <androidx.cardview.widget.CardView
        android:id="@+id/search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="8dp"
        android:visibility="gone"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <EditText
                android:id="@+id/search_edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="Search participants..."
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:padding="8dp"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurface"
                android:textColorHint="?attr/colorOnSurface"
                android:alpha="0.6" />

            <ImageView
                android:id="@+id/clear_search_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:padding="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                app:tint="?attr/colorOnSurface" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Content container with white background -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_container">

        <!-- Info text at the top -->
        <TextView
            android:id="@+id/info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="#FFCC00"
            android:padding="12dp"
            android:text="Toggle permissions for group members. Members without permission cannot send messages or files."
            android:textColor="#0D0302"
            android:textSize="14sp"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Divider -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="#E0E0E0"
            app:layout_constraintTop_toBottomOf="@id/info_text" />

        <!-- Save Changes Button -->
        <Button
            android:id="@+id/save_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_button"
            android:backgroundTint="#006BFF"
            android:padding="12dp"
            android:text="Save Changes"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- RecyclerView for participants list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/participants_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/save_button"
            app:layout_constraintTop_toBottomOf="@id/divider"
            tools:listitem="@layout/item_permission" />

        <!-- Progress bar -->
        <ProgressBar
            android:id="@+id/loading_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 