package com.phad.chatapp.fragments

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.fragment.app.Fragment
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.R
import com.phad.chatapp.LoginActivity
import com.phad.chatapp.utils.SessionManager

class ProfileFragment : Fragment() {
    private val TAG = "ProfileFragment"
    private lateinit var sessionManager: SessionManager
    private lateinit var db: FirebaseFirestore
    
    // UI elements
    private lateinit var tvName: TextView
    private lateinit var tvLocation: TextView
    private lateinit var tvEmail: TextView
    private lateinit var tvPhone: TextView
    private lateinit var tvRollNumber: TextView
    private lateinit var tvEmail2: TextView
    private lateinit var tvAcademicGroup: TextView
    private lateinit var tvNssGroup: TextView
    private lateinit var tvTopicsHeader: TextView
    private lateinit var tvTopic1: TextView
    private lateinit var tvTopic2: TextView
    private lateinit var tvTopic3: TextView
    private lateinit var tvEvents: TextView
    private lateinit var tvClasses: TextView
    private lateinit var tvMeetings: TextView
    private lateinit var btnLogout: Button
    private lateinit var ivTaskIcon: ImageView
    private lateinit var ivChatbotIcon: ImageView

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_profile, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        // Initialize Firebase
        db = FirebaseFirestore.getInstance()
        
        // Initialize SessionManager
        sessionManager = SessionManager(requireContext())
        
        // Check if legacy SessionManager exists
        val legacyPref = requireContext().getSharedPreferences("ChatAppPref", Context.MODE_PRIVATE)
        val usingLegacyManager = legacyPref.contains("isLoggedIn")
        if (usingLegacyManager) {
            Log.d(TAG, "Legacy SessionManager detected - will handle both implementations")
        }
        
        // Initialize UI elements
        initViews(view)
        
        // Set up logout button
        btnLogout.setOnClickListener {
            // Log the user out
            try {
                Log.d(TAG, "Logout button clicked, performing logout...")
                
                // Sign out from Firebase Auth first
                val firebaseAuth = com.google.firebase.auth.FirebaseAuth.getInstance()
                firebaseAuth.signOut()
                Log.d(TAG, "Firebase Auth signOut called")
                
                // Then clear session data using the utils SessionManager
                sessionManager.logoutUser()
                Log.d(TAG, "SessionManager.logoutUser() called")
                
                // Also clear legacy SessionManager data if it exists
                if (usingLegacyManager) {
                    try {
                        val legacyManager = com.phad.chatapp.SessionManager(requireContext())
                        legacyManager.logoutUser()
                        Log.d(TAG, "Legacy SessionManager data cleared")
                    } catch (e: Exception) {
                        Log.e(TAG, "Error clearing legacy SessionManager data: ${e.message}")
                    }
                }
                
                Log.d(TAG, "Logout successful, redirecting to login screen")
                
                // Redirect to login activity - make sure we're completely exiting the app
                val intent = Intent(requireActivity(), LoginActivity::class.java)
                intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                intent.putExtra("LOGOUT", true)  // Add flag to indicate we're coming from logout
                startActivity(intent)
                
                // Finish all activities in the stack
                requireActivity().finishAffinity()
            } catch (e: Exception) {
                Log.e(TAG, "Error during logout: ${e.message}", e)
                Toast.makeText(requireContext(), "Logout failed: ${e.message}", Toast.LENGTH_LONG).show()
            }
        }
        
        // Set up icon clicks
        setupIconClicks()
        
        // Load user profile based on role
        loadUserProfile()
    }
    
    private fun initViews(view: View) {
        tvName = view.findViewById(R.id.tvName)
        tvLocation = view.findViewById(R.id.tvLocation)
        tvEmail = view.findViewById(R.id.tvEmail)
        tvPhone = view.findViewById(R.id.tvPhone)
        tvRollNumber = view.findViewById(R.id.tvRollNumber)
        tvEmail2 = view.findViewById(R.id.tvEmail2)
        tvAcademicGroup = view.findViewById(R.id.tvAcademicGroup)
        tvNssGroup = view.findViewById(R.id.tvNssGroup)
        tvTopicsHeader = view.findViewById(R.id.tvTopicsHeader)
        tvTopic1 = view.findViewById(R.id.tvTopic1)
        tvTopic2 = view.findViewById(R.id.tvTopic2)
        tvTopic3 = view.findViewById(R.id.tvTopic3)
        tvEvents = view.findViewById(R.id.tvEvents)
        tvClasses = view.findViewById(R.id.tvClasses)
        tvMeetings = view.findViewById(R.id.tvMeetings)
        btnLogout = view.findViewById(R.id.btnLogout)
        ivTaskIcon = view.findViewById(R.id.ivTaskIcon)
        ivChatbotIcon = view.findViewById(R.id.ivChatbotIcon)
    }
    
    private fun setupIconClicks() {
        ivTaskIcon.setOnClickListener {
            // Same functionality as Home feature's task icon
            Toast.makeText(context, "Task icon clicked", Toast.LENGTH_SHORT).show()
        }
        
        ivChatbotIcon.setOnClickListener {
            // Launch the ChatBotActivity
            val intent = Intent(requireContext(), com.phad.chatapp.features.home.chatbot.ui.ChatBotActivity::class.java)
            startActivity(intent)
        }
    }
    
    private fun loadUserProfile() {
        val userType = sessionManager.fetchUserType()
        val userId = sessionManager.fetchUserId()
        val rollNumber = sessionManager.fetchRollNumber() ?: ""
        
        // Additional debug to verify roll number
        Log.d(TAG, "SessionManager.fetchUserId(): $userId")
        Log.d(TAG, "SessionManager.fetchRollNumber(): $rollNumber")
        Log.d(TAG, "SessionManager.fetchUserType(): $userType")
        
        // Get user data directly from SharedPreferences to debug the issue
        val pref = context?.getSharedPreferences("ChatAppPref", Context.MODE_PRIVATE)
        val altRollNumber = pref?.getString("rollNumber", "")
        
        // Also try the utils SessionManager format
        val utilsPref = context?.getSharedPreferences("ChatAppSession", Context.MODE_PRIVATE)
        val utilsRollNumber = utilsPref?.getString("userRollNumber", "")
        
        Log.d(TAG, "Direct SharedPreferences - rollNumber: $altRollNumber")
        Log.d(TAG, "Utils SharedPreferences - userRollNumber: $utilsRollNumber")
        
        // Get current Firebase Auth user
        val firebaseUser = FirebaseAuth.getInstance().currentUser
        val firebaseEmail = firebaseUser?.email
        Log.d(TAG, "Firebase current user email: $firebaseEmail")
        
        // Clear previous data
        resetProfileUI()
        
        // Choose which identifier to use - checking all possible sources
        val userIdentifier = when {
            rollNumber.isNotEmpty() -> {
                Log.d(TAG, "Using rollNumber from sessionManager.fetchRollNumber()")
                rollNumber
            }
            altRollNumber?.isNotEmpty() == true -> {
                Log.d(TAG, "Using rollNumber from direct SharedPreferences")
                altRollNumber
            }
            utilsRollNumber?.isNotEmpty() == true -> {
                Log.d(TAG, "Using userRollNumber from utils SharedPreferences")
                utilsRollNumber
            }
            userId?.isNotEmpty() == true -> {
                Log.d(TAG, "Using userId from sessionManager.fetchUserId()")
                userId
            }
            firebaseEmail != null -> {
                Log.d(TAG, "Looking up user by email: $firebaseEmail")
                // First try to find the user by email
                findUserByEmail(firebaseEmail)
                return
            }
            else -> {
                Log.e(TAG, "No identifier available to load profile")
                Toast.makeText(context, "Unable to load profile: No user ID or roll number found", Toast.LENGTH_LONG).show()
                return
            }
        }
        
        Log.d(TAG, "Using identifier to load profile: $userIdentifier")
        
        // Try to load from all potential collections to find the correct profile
        tryLoadFromAllCollections(userIdentifier)
    }
    
    private fun findUserByEmail(email: String) {
        db.collection("users")
            .whereEqualTo("email", email)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val userDoc = documents.documents[0]
                    val rollNumber = userDoc.getString("rollNumber")
                    Log.d(TAG, "Found user by email with rollNumber: $rollNumber")
                    
                    if (rollNumber != null) {
                        tryLoadFromAllCollections(rollNumber)
                    } else {
                        Log.e(TAG, "User found by email but has no roll number")
                        Toast.makeText(context, "User found but profile data is incomplete", Toast.LENGTH_SHORT).show()
                    }
                } else {
                    Log.e(TAG, "No user found with email: $email")
                    Toast.makeText(context, "Unable to load profile: User not found", Toast.LENGTH_SHORT).show()
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error searching for user by email: ${e.message}")
                Toast.makeText(context, "Error loading profile: ${e.message}", Toast.LENGTH_SHORT).show()
            }
    }
    
    private fun tryLoadFromAllCollections(identifier: String) {
        Log.d(TAG, "Attempting to load from all collections for identifier: $identifier")
        
        // First check in the users collection to verify user type and get the actual roll number
        db.collection("users")
            .whereEqualTo("rollNumber", identifier)
            .get()
            .addOnSuccessListener { userDocs ->
                if (!userDocs.isEmpty) {
                    val userDoc = userDocs.documents[0]
                    val userType = userDoc.getString("userType") ?: ""
                    val rollNumber = userDoc.getString("rollNumber") ?: identifier
                    Log.d(TAG, "Found in users collection with userType: $userType, rollNumber: $rollNumber")
                    
                    // Based on user type, load from the correct collection using the roll number
                    when {
                        userType.equals("Admin1", ignoreCase = true) -> loadFromAdmin1Collection(rollNumber.toString())
                        userType.equals("Admin2", ignoreCase = true) -> loadFromAdmin2Collection(rollNumber.toString())
                        else -> loadFromStudentCollection(rollNumber.toString())
                    }
                } else {
                    // Not found with rollNumber, try with the document ID
                    db.collection("users")
                        .document(identifier)
                        .get()
                        .addOnSuccessListener { document ->
                            if (document.exists()) {
                                val userType = document.getString("userType") ?: ""
                                val rollNumber = document.getString("rollNumber") ?: identifier
                                Log.d(TAG, "Found in users collection by document ID with userType: $userType, rollNumber: $rollNumber")
                                
                                // Based on user type, load from the correct collection
                                when {
                                    userType.equals("Admin1", ignoreCase = true) -> loadFromAdmin1Collection(rollNumber.toString())
                                    userType.equals("Admin2", ignoreCase = true) -> loadFromAdmin2Collection(rollNumber.toString())
                                    else -> loadFromStudentCollection(rollNumber.toString())
                                }
                            } else {
                                Log.d(TAG, "Not found in users collection, trying direct collection search")
                                // Try direct collection approach as fallback
                                tryDirectCollectionAccess(identifier)
                            }
                        }
                        .addOnFailureListener { e ->
                            Log.e(TAG, "Error getting user document: ${e.message}")
                            tryDirectCollectionAccess(identifier)
                        }
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error querying users collection: ${e.message}")
                tryDirectCollectionAccess(identifier)
            }
    }
    
    private fun tryDirectCollectionAccess(rollNumber: String) {
        // Try to find the user in each collection directly
        db.collection("Student")  // Note: singular "Student" collection
            .whereEqualTo("Roll_No_", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val docId = documents.documents[0].id
                    Log.d(TAG, "Found user in Student collection with doc ID: $docId")
                    loadStudentProfile(docId)
                } else {
                    // Try Admin1 collection
                    db.collection("Admin1")
                        .whereEqualTo("Roll_No", rollNumber)
                        .get()
                        .addOnSuccessListener { adminDocs ->
                            if (!adminDocs.isEmpty) {
                                val docId = adminDocs.documents[0].id
                                Log.d(TAG, "Found user in Admin1 collection with doc ID: $docId")
                                loadAdmin1Profile(docId)
                            } else {
                                // Try Admin2 collection
                                db.collection("Admin2")
                                    .whereEqualTo("Roll_No", rollNumber)
                                    .get()
                                    .addOnSuccessListener { admin2Docs ->
                                        if (!admin2Docs.isEmpty) {
                                            val docId = admin2Docs.documents[0].id
                                            Log.d(TAG, "Found user in Admin2 collection with doc ID: $docId")
                                            loadAdmin2Profile(docId)
                                        } else {
                                            Log.e(TAG, "User not found in any collection")
                                            Toast.makeText(context, "Profile not found for roll number: $rollNumber", Toast.LENGTH_LONG).show()
                                        }
                                    }
                            }
                        }
                }
            }
    }
    
    private fun loadFromStudentCollection(rollNumber: String) {
        db.collection("Student")
            .whereEqualTo("Roll_No_", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val docId = documents.documents[0].id
                    loadStudentProfile(docId)
                } else {
                    Log.e(TAG, "User not found in Student collection with Roll_No_: $rollNumber")
                    Toast.makeText(context, "Student profile not found", Toast.LENGTH_SHORT).show()
                }
            }
    }
    
    private fun loadFromAdmin1Collection(rollNumber: String) {
        db.collection("Admin1")
            .whereEqualTo("Roll_No", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val docId = documents.documents[0].id
                    loadAdmin1Profile(docId)
                } else {
                    Log.e(TAG, "User not found in Admin1 collection with Roll_No: $rollNumber")
                    Toast.makeText(context, "Admin profile not found", Toast.LENGTH_SHORT).show()
                }
            }
    }
    
    private fun loadFromAdmin2Collection(rollNumber: String) {
        db.collection("Admin2")
            .whereEqualTo("Roll_No", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val docId = documents.documents[0].id
                    loadAdmin2Profile(docId)
                } else {
                    Log.e(TAG, "User not found in Admin2 collection with Roll_No: $rollNumber")
                    Toast.makeText(context, "Admin profile not found", Toast.LENGTH_SHORT).show()
                }
            }
    }

    private fun loadStudentProfile(docId: String) {
        Log.d(TAG, "Loading Student profile for document ID: $docId")
        db.collection("Student")
            .document(docId)
            .get()
            .addOnSuccessListener { document ->
                if (document != null && document.exists()) {
                    Log.d(TAG, "Student document data: ${document.data}")
                    
                    val name = document.getString("Name") ?: "Student"
                    val gender = document.getString("Sex") ?: ""
                    val email = document.getString("Gmail_ID") ?: ""
                    val phone = document.getString("Mobile_no_") ?: ""
                    val roll = document.getString("Roll_No_") ?: ""
                    val instituteEmail = document.getString("Institute_ID") ?: ""
                    val academicGroup = document.getString("Academic_Grp_") ?: "0"
                    val nssGroup = document.getString("NSS_gro") ?: "-1"
                    val subject1 = document.getString("Subjec_Prefrence_1") ?: ""
                    val subject2 = document.getString("Sub__Preference2") ?: ""
                    val subject3 = document.getString("Sub__Preference_3") ?: ""
                    
                    // Update UI with student data
                    tvName.text = name
                    tvLocation.text = gender
                    tvEmail.text = email
                    tvPhone.text = phone
                    tvRollNumber.text = roll
                    tvEmail2.text = instituteEmail
                    
                    tvAcademicGroup.text = "Academic Group: $academicGroup"
                    tvNssGroup.text = "NSS Group: $nssGroup"
                    
                    // Update subjects
                    tvTopicsHeader.text = "Topics of Interest"
                    tvTopic1.text = subject1
                    tvTopic2.text = subject2
                    tvTopic3.text = subject3
                    
                    // Load statistics
                    loadStatistics()
                    
                    Log.d(TAG, "Successfully loaded Student profile: $name")
                } else {
                    Log.e(TAG, "Student document doesn't exist for document ID: $docId")
                    // Try the query approach if direct document access fails
                    queryStudentByRollNumber(docId)
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading Student profile: ${e.message}")
                // Try the query approach if direct document access fails
                queryStudentByRollNumber(docId)
            }
    }
    
    private fun queryStudentByRollNumber(rollNumber: String) {
        Log.d(TAG, "Querying Student collection by rollNumber: $rollNumber")
        db.collection("Student")
            .whereEqualTo("Roll_No_", rollNumber)
            .get()
            .addOnSuccessListener { documents ->
                if (!documents.isEmpty) {
                    val document = documents.documents[0]
                    Log.d(TAG, "Found Student by query: ${document.id}")
                    
                    val name = document.getString("Name") ?: "Student"
                    val gender = document.getString("Sex") ?: ""
                    val email = document.getString("Gmail_ID") ?: ""
                    val phone = document.getString("Mobile_no_") ?: ""
                    val roll = document.getString("Roll_No_") ?: ""
                    val instituteEmail = document.getString("Institute_ID") ?: ""
                    val academicGroup = document.getString("Academic_Grp_") ?: "0"
                    val nssGroup = document.getString("NSS_gro") ?: "-1"
                    val subject1 = document.getString("Subjec_Prefrence_1") ?: ""
                    val subject2 = document.getString("Sub__Preference2") ?: ""
                    val subject3 = document.getString("Sub__Preference_3") ?: ""
                    
                    // Update UI with student data
                    tvName.text = name
                    tvLocation.text = gender
                    tvEmail.text = email
                    tvPhone.text = phone
                    tvRollNumber.text = roll
                    tvEmail2.text = instituteEmail
                    
                    tvAcademicGroup.text = "Academic Group: $academicGroup"
                    tvNssGroup.text = "NSS Group: $nssGroup"
                    
                    // Update subjects
                    tvTopicsHeader.text = "Topics of Interest"
                    tvTopic1.text = subject1
                    tvTopic2.text = subject2
                    tvTopic3.text = subject3
                    
                    // Load statistics
                    loadStatistics()
                    
                    Log.d(TAG, "Successfully loaded Student profile by query: $name")
                } else {
                    Log.e(TAG, "No student found with roll number: $rollNumber")
                    Toast.makeText(context, "Profile not found for roll number: $rollNumber", Toast.LENGTH_SHORT).show()
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error querying Student collection: ${e.message}")
                Toast.makeText(context, "Error loading profile: ${e.message}", Toast.LENGTH_SHORT).show()
            }
    }
    
    private fun resetProfileUI() {
        // Reset all text views to empty or default values
        tvName.text = ""
        tvLocation.text = ""
        tvEmail.text = ""
        tvPhone.text = ""
        tvRollNumber.text = ""
        tvEmail2.text = ""
        tvAcademicGroup.text = ""
        tvNssGroup.text = ""
        tvTopicsHeader.text = "Topics of Interest"
        tvTopic1.text = ""
        tvTopic2.text = ""
        tvTopic3.text = ""
        tvEvents.text = "0/0"
        tvClasses.text = "0/0"
        tvMeetings.text = "0/0"
        
        // Make all views visible (will hide as needed)
        tvAcademicGroup.visibility = View.VISIBLE
        tvNssGroup.visibility = View.VISIBLE
        tvTopicsHeader.visibility = View.VISIBLE
        tvTopic1.visibility = View.VISIBLE
        tvTopic2.visibility = View.VISIBLE
        tvTopic3.visibility = View.VISIBLE
    }
    
    private fun loadAdmin1Profile(docId: String) {
        Log.d(TAG, "Loading Admin1 profile for document ID: $docId")
        db.collection("Admin1")
            .document(docId)
            .get()
            .addOnSuccessListener { document ->
                if (document != null && document.exists()) {
                    Log.d(TAG, "Admin1 document data: ${document.data}")
                    
                    val name = document.getString("Name") ?: "Admin"
                    val email = document.getString("Email_ID_Google") ?: ""
                    val collegeEmail = document.getString("Email_ID_college") ?: ""
                    val phone = document.getString("Mobile_Number") ?: ""
                    val roll = document.getString("Roll_No") ?: ""
                    val designation = document.getString("Designation") ?: ""
                    val committeeCode = document.getString("Commitee_Code") ?: ""
                    
                    // Update UI with admin data
                    tvName.text = name
                    tvLocation.text = designation
                    tvEmail.text = email
                    tvPhone.text = phone
                    tvRollNumber.text = roll
                    tvEmail2.text = collegeEmail
                    
                    // Hide or repurpose student-specific fields
                    tvAcademicGroup.text = "Committee: $committeeCode"
                    tvNssGroup.visibility = View.GONE
                    
                    tvTopicsHeader.text = "Course Codes"
                    tvTopic1.text = document.getString("Course_Code") ?: ""
                    tvTopic2.visibility = View.GONE
                    tvTopic3.visibility = View.GONE
                    
                    // Load statistics
                    loadStatistics()
                    
                    Log.d(TAG, "Successfully loaded Admin1 profile: $name")
                } else {
                    Log.e(TAG, "Admin1 document doesn't exist for document ID: $docId")
                    Toast.makeText(context, "Admin profile not found", Toast.LENGTH_SHORT).show()
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading Admin1 profile: ${e.message}")
                Toast.makeText(context, "Error loading profile: ${e.message}", Toast.LENGTH_SHORT).show()
            }
    }
    
    private fun loadAdmin2Profile(docId: String) {
        Log.d(TAG, "Loading Admin2 profile for document ID: $docId")
        db.collection("Admin2")
            .document(docId)
            .get()
            .addOnSuccessListener { document ->
                if (document != null && document.exists()) {
                    Log.d(TAG, "Admin2 document data: ${document.data}")
                    
                    val name = document.getString("Name") ?: "Admin"
                    val email = document.getString("Email_ID_Google") ?: ""
                    val collegeEmail = document.getString("Email_ID_college") ?: ""
                    val phone = document.getString("Mobile_Number") ?: ""
                    val roll = document.getString("Roll_No") ?: ""
                    val designation = document.getString("Designation") ?: ""
                    
                    // Update UI with admin data
                    tvName.text = name
                    tvLocation.text = designation
                    tvEmail.text = email
                    tvPhone.text = phone
                    tvRollNumber.text = roll
                    tvEmail2.text = collegeEmail
                    
                    // Hide student-specific fields
                    tvAcademicGroup.visibility = View.GONE
                    tvNssGroup.visibility = View.GONE
                    tvTopicsHeader.visibility = View.GONE
                    tvTopic1.visibility = View.GONE
                    tvTopic2.visibility = View.GONE
                    tvTopic3.visibility = View.GONE
                    
                    // Load statistics
                    loadStatistics()
                    
                    Log.d(TAG, "Successfully loaded Admin2 profile: $name")
                } else {
                    Log.e(TAG, "Admin2 document doesn't exist for document ID: $docId")
                    Toast.makeText(context, "Admin profile not found", Toast.LENGTH_SHORT).show()
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Error loading Admin2 profile: ${e.message}")
                Toast.makeText(context, "Error loading profile: ${e.message}", Toast.LENGTH_SHORT).show()
            }
    }
    
    private fun loadStatistics() {
        val userId = sessionManager.fetchUserId()
        val rollNumber = sessionManager.fetchRollNumber()
        
        // Check for null values
        if (userId.isEmpty()) {
            Log.e(TAG, "Cannot load statistics: User ID is empty")
            return
        }
        
        if (rollNumber == null) {
            Log.e(TAG, "Cannot load statistics: Roll number is null")
            return
        }
        
        Log.d(TAG, "Loading statistics for userId: $userId, rollNumber: $rollNumber")
        
        // Load events statistics
        db.collection("events")
            .whereEqualTo("userId", userId)
            .get()
            .addOnSuccessListener { documents ->
                val totalEvents = documents.size()
                val completedEvents = documents.count { it.getString("status") == "COMPLETED" }
                tvEvents.text = "$completedEvents/$totalEvents"
                Log.d(TAG, "Loaded events statistics: $completedEvents/$totalEvents")
            }
            .addOnFailureListener {
                tvEvents.text = "0/0"
                Log.e(TAG, "Error loading events statistics")
            }
        
        // Load classes statistics
        db.collection("calendar_events")
            .whereEqualTo("bookedBy", userId)
            .whereEqualTo("eventType", "TEACHING")
            .get()
            .addOnSuccessListener { documents ->
                val totalClasses = documents.size()
                val attendedClasses = documents.count { it.getString("status") == "COMPLETED" }
                tvClasses.text = "$attendedClasses/$totalClasses"
                Log.d(TAG, "Loaded classes statistics: $attendedClasses/$totalClasses")
            }
            .addOnFailureListener {
                tvClasses.text = "0/0"
                Log.e(TAG, "Error loading classes statistics")
            }
        
        // Load meetings statistics
        db.collection("meetings")
            .whereEqualTo("participantId", userId)
            .get()
            .addOnSuccessListener { documents ->
                val totalMeetings = documents.size()
                val attendedMeetings = documents.count { it.getString("status") == "COMPLETED" }
                tvMeetings.text = "$attendedMeetings/$totalMeetings"
                Log.d(TAG, "Loaded meetings statistics: $attendedMeetings/$totalMeetings")
            }
            .addOnFailureListener {
                // In case of error, set default value
                tvMeetings.text = "0/0"
                Log.e(TAG, "Error loading meetings statistics")
            }
    }
    
    companion object {
        /**
         * Create a new instance of ProfileFragment
         */
        fun newInstance(): ProfileFragment {
            return ProfileFragment()
        }
    }
} 