<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    app:cardCornerRadius="6dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="#1A1A1A">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="8dp"
        android:background="#1A1A1A">

        <View
            android:id="@+id/unread_indicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:background="#006BFF"
            android:layout_marginEnd="8dp" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_sender_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:textStyle="bold"
                android:maxLines="1"
                android:ellipsize="end" />

            <TextView
                android:id="@+id/text_message_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#DDDDDD"
                android:textSize="13sp"
                android:maxLines="1"
                android:ellipsize="end" />

        </LinearLayout>

        <TextView
            android:id="@+id/text_timestamp"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="#AAAAAA"
            android:textSize="12sp" />

    </LinearLayout>

</androidx.cardview.widget.CardView> 