<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary"
    android:padding="16dp">

    <!-- Header section -->
    <TextView
        android:id="@+id/tvCalendarTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Calendar"
        android:textColor="@color/secondary"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/ivBackButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:contentDescription="Back"
        android:src="@android:drawable/ic_menu_revert"
        app:layout_constraintBottom_toBottomOf="@id/tvCalendarTitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@id/tvCalendarTitle"
        app:tint="@color/secondary" />

    <!-- Month selection bar -->
    <TextView
        android:id="@+id/tvMonthYear"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@color/primary_variant"
        android:gravity="center"
        android:padding="8dp"
        android:text="May 2023"
        android:textColor="@color/secondary"
        android:textSize="18sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvCalendarTitle" />

    <!-- Weekday header -->
    <LinearLayout
        android:id="@+id/layoutWeekdayHeader"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:background="@color/primary_variant"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvMonthYear">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="SUN"
            android:textColor="@color/accent"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="MON"
            android:textColor="@color/secondary"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="TUE"
            android:textColor="@color/secondary"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="WED"
            android:textColor="@color/secondary"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="THU"
            android:textColor="@color/secondary"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="FRI"
            android:textColor="@color/secondary"
            android:textSize="12sp" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center"
            android:text="SAT"
            android:textColor="@color/accent"
            android:textSize="12sp" />
    </LinearLayout>

    <!-- Calendar grid -->
    <GridView
        android:id="@+id/gridCalendar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="1dp"
        android:background="@color/primary_variant"
        android:horizontalSpacing="1dp"
        android:numColumns="7"
        android:padding="4dp"
        android:stretchMode="columnWidth"
        android:verticalSpacing="1dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layoutWeekdayHeader" />

    <!-- Secondary selection bar -->
    <TextView
        android:id="@+id/tvSecondaryBar"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:background="@color/accent"
        android:gravity="center"
        android:padding="8dp"
        android:text="Upcoming Events"
        android:textColor="@color/secondary"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/gridCalendar" />

    <!-- Events list -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/scrollView"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        app:layout_constraintBottom_toTopOf="@id/layoutActionButtons"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tvSecondaryBar">

        <LinearLayout
            android:id="@+id/layoutEvents"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <!-- Sample event items will be replaced dynamically -->
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
    
    <!-- Progress indicator -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Action buttons -->
    <LinearLayout
        android:id="@+id/layoutActionButtons"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:background="@color/primary_variant"
        android:gravity="center"
        android:orientation="horizontal"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <ImageButton
            android:id="@+id/btnAction1"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="4dp"
            android:layout_weight="1"
            android:background="@color/accent"
            android:contentDescription="Apply for Leave"
            android:src="@android:drawable/ic_menu_edit"
            app:tint="@color/secondary" />

        <ImageButton
            android:id="@+id/btnAction2"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="4dp"
            android:layout_weight="1"
            android:background="@color/accent"
            android:contentDescription="Book Slot"
            android:src="@android:drawable/ic_menu_my_calendar"
            app:tint="@color/secondary" />

        <ImageButton
            android:id="@+id/btnAction3"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="4dp"
            android:layout_weight="1"
            android:background="@color/accent"
            android:contentDescription="View Details"
            android:src="@android:drawable/ic_menu_info_details"
            app:tint="@color/secondary" />

        <ImageButton
            android:id="@+id/btnAction4"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_margin="4dp"
            android:layout_weight="1"
            android:background="@color/accent"
            android:contentDescription="Settings"
            android:src="@android:drawable/ic_menu_manage"
            app:tint="@color/secondary" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout> 