<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context=".activities.UpdateDetailActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fitsSystemWindows="true"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="?attr/colorPrimary"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />
    </com.google.android.material.appbar.AppBarLayout>

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior" />

    <!-- Error TextView -->
    <TextView
        android:id="@+id/errorTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:gravity="center"
        android:padding="16dp"
        android:textColor="@android:color/holo_red_dark"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_behavior="@string/appbar_scrolling_view_behavior"
        tools:text="Error loading update" />

    <androidx.core.widget.NestedScrollView
        android:id="@+id/contentScrollView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">
        
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Author info -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingBottom="16dp">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/authorImageView"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:src="@drawable/default_profile_image"
                    app:civ_border_width="1dp"
                    app:civ_border_color="#EEEEEE"/>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginStart="12dp">

                    <TextView
                        android:id="@+id/authorNameTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textStyle="bold"
                        android:textSize="16sp"
                        android:textColor="?attr/colorOnSurface"
                        tools:text="John Doe"/>

                    <TextView
                        android:id="@+id/timestampTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.6"
                        tools:text="May 15, 2023 at 2:30 PM"/>
                        
                    <TextView
                        android:id="@+id/rollNumberTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="12sp"
                        android:textColor="?attr/colorOnSurface"
                        android:alpha="0.8"
                        tools:text="Roll Number: 21CS01"/>
                </LinearLayout>
            </LinearLayout>

            <!-- Title (if any) -->
            <TextView
                android:id="@+id/titleTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="?attr/colorOnSurface"
                tools:text="Update Title"/>

            <!-- Content -->
            <TextView
                android:id="@+id/contentTextView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurface"
                tools:text="This is a sample update content with some detailed text that shows the complete message. This is where all the content would be displayed without any truncation."/>

            <!-- Media Image (if any) -->
            <ImageView
                android:id="@+id/imageView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:adjustViewBounds="true"
                android:scaleType="fitCenter"
                android:maxHeight="400dp"
                android:visibility="gone"
                tools:visibility="visible"
                tools:src="@android:drawable/ic_menu_gallery"/>

            <!-- External link (if any) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/externalLinkCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="#F5F5F5"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_view"
                        app:tint="#757575"/>

                    <TextView
                        android:id="@+id/externalLinkText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:textSize="14sp"
                        tools:text="https://example.com"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Document (if any) -->
            <androidx.cardview.widget.CardView
                android:id="@+id/documentCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="8dp"
                app:cardBackgroundColor="#F5F5F5"
                android:visibility="gone"
                tools:visibility="visible">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@android:drawable/ic_menu_agenda"
                        app:tint="#757575"/>

                    <TextView
                        android:id="@+id/documentNameText"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="12dp"
                        android:textSize="14sp"
                        tools:text="document_name.pdf"/>
                </LinearLayout>
            </androidx.cardview.widget.CardView>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.coordinatorlayout.widget.CoordinatorLayout> 