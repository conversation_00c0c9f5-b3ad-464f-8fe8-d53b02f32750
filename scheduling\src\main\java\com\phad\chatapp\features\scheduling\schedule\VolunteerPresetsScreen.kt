package com.phad.chatapp.features.scheduling.schedule

import android.util.Log
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.core.FastOutSlowInEasing
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.scaleIn
import androidx.compose.animation.scaleOut
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import androidx.compose.foundation.layout.Arrangement
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.features.scheduling.firebase.FirebaseManager
import com.phad.chatapp.features.scheduling.models.VolunteerPreset as ModelVolunteerPreset
import com.phad.chatapp.features.scheduling.ui.theme.*
import com.phad.chatapp.features.scheduling.schedule.StandardButton
import kotlinx.coroutines.launch
import com.google.firebase.firestore.WriteBatch
import java.text.SimpleDateFormat
import java.util.*

// Local VolunteerPreset class for this file
data class VolunteerPreset(
    val id: String = "",
    val name: String = "",
    val createdAt: Long = 0,
    val volunteerCount: Int = 0,
    val groupCounts: Map<String, Int> = emptyMap()
)

private const val TAG = "VolunteerPresetsScreen"

// Natural sorting function to handle numbers correctly (AM 9B before AM 10G)
private fun naturalSortKey(text: String): String {
    return text.replace(Regex("\\d+")) { matchResult ->
        matchResult.value.padStart(10, '0')
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun VolunteerPresetsScreen(navController: NavController) {
    val snackbarHostState = remember { SnackbarHostState() }
    val coroutineScope = rememberCoroutineScope()

    // State for presets from database
    var presets by remember { mutableStateOf<List<VolunteerPreset>>(emptyList()) }
    var isLoading by remember { mutableStateOf(true) }
    var errorMessage by remember { mutableStateOf<String?>(null) }

    // Dialog states
    var showRenameDialog by remember { mutableStateOf(false) }
    var showDeleteDialog by remember { mutableStateOf(false) }
    var presetToRename by remember { mutableStateOf<VolunteerPreset?>(null) }
    var presetToDelete by remember { mutableStateOf<VolunteerPreset?>(null) }
    var newPresetName by remember { mutableStateOf("") }

    // Animation states following UI.md timing pattern (100ms→200ms→100ms)
    var topBarVisible by remember { mutableStateOf(false) }
    var contentVisible by remember { mutableStateOf(false) }
    var listVisible by remember { mutableStateOf(false) }

    // Animation reset trigger for consistent animations on every screen visit
    var animationTrigger by remember { mutableStateOf(0) }

    // Animation control flag for accessibility compliance
    var animationsEnabled by remember { mutableStateOf(true) }

    // 1.5-second animation timeout that starts after loading completes (UI.md specification)
    LaunchedEffect(isLoading) {
        if (!isLoading) {
            // Start with animations enabled when loading completes
            animationsEnabled = true

            // Disable animations after 1.5 seconds from when loading completes
            kotlinx.coroutines.delay(1500)
            animationsEnabled = false
        }
    }

    // Track which items have been animated to prevent re-animation during scrolling
    val animatedItems = remember { mutableSetOf<String>() }

    // Function to load presets from Firestore
    fun loadPresets(createDefaultIfEmpty: Boolean = true) {
        isLoading = true
        errorMessage = null

        FirebaseManager.getInstance().getCollection(
            "volunteerPresets",
            onSuccess = { snapshot ->
                try {
                    val presetsList = mutableListOf<VolunteerPreset>()

                    for (document in snapshot.documents) {
                        val id = document.id
                        val name = document.getString("name") ?: "Unnamed Preset"
                        val createdAt = document.getLong("createdAt") ?: System.currentTimeMillis()
                        val volunteerCount = document.getLong("volunteerCount")?.toInt() ?: 0

                        // Get group counts from Firestore if available
                        var groupCounts = emptyMap<String, Int>()
                        val groupCountsData = document.get("groupCounts") as? Map<*, *>
                        if (groupCountsData != null) {
                            // Convert the data to the correct type
                            groupCounts = groupCountsData.mapKeys { it.key.toString() }
                                .mapValues {
                                    when (val value = it.value) {
                                        is Long -> value.toInt()
                                        is Int -> value
                                        else -> 0
                                    }
                                }
                            Log.d(TAG, "Loaded group counts from Firestore: $groupCounts")
                        }

                        // Create the preset with all the info
                        val preset = VolunteerPreset(
                            id = id,
                            name = name,
                            createdAt = createdAt,
                            volunteerCount = volunteerCount,
                            groupCounts = groupCounts
                        )

                        presetsList.add(preset)

                        // If group counts are not available in Firestore, calculate them from the volunteers array
                        if (groupCounts.isEmpty()) {
                            // Get volunteers array from the preset document
                            val volunteersData = document.get("volunteers") as? List<Map<String, Any>>
                            if (volunteersData != null) {
                                val newGroupCounts = mutableMapOf<String, Int>()

                                for (volunteerMap in volunteersData) {
                                    val group = volunteerMap["group"] as? String ?: "0"
                                    newGroupCounts[group] = (newGroupCounts[group] ?: 0) + 1
                                }

                                // Update the preset with group statistics
                                val updatedPresets = presets.map {
                                    if (it.id == preset.id) {
                                        it.copy(groupCounts = newGroupCounts)
                                    } else {
                                        it
                                    }
                                }

                                presets = updatedPresets
                            }
                        }
                    }

                    // Sort alphabetically by name (natural sorting for names with numbers)
                    presets = presetsList.sortedWith(compareBy { naturalSortKey(it.name) })
                    isLoading = false

                    if (presetsList.isEmpty() && createDefaultIfEmpty) {
                        // Create a default preset if none exist
                        isLoading = true

                        val presetData = hashMapOf(
                            "name" to "Default Preset",
                            "createdAt" to System.currentTimeMillis()
                        )

                        FirebaseManager.getInstance().addDocument(
                            "volunteerPresets",
                            presetData,
                            onSuccess = { documentRef ->
                                isLoading = false
                                // Reload presets without creating default preset
                                loadPresets(false)
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("Default preset created")
                                }
                            },
                            onFailure = { e ->
                                isLoading = false
                                Log.e(TAG, "Error creating default preset: ${e.message}", e)
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("Failed to create default preset: ${e.message}")
                                }
                            }
                        )
                    }

                } catch (e: Exception) {
                    Log.e(TAG, "Error processing presets: ${e.message}", e)
                    errorMessage = "Error loading presets: ${e.message}"
                    isLoading = false
                }
            },
            onFailure = { e ->
                Log.e(TAG, "Firestore error: ${e.message}", e)
                errorMessage = "Firestore error: ${e.message}"
                isLoading = false
            }
        )
    }

    // Function to create a new preset using name as document ID
    fun createNewPreset(name: String) {
        isLoading = true

        val presetData = hashMapOf(
            "name" to name
        )

        FirebaseManager.getInstance().setDocument(
            "volunteerPresets/$name",
            presetData,
            onSuccess = {
                isLoading = false
                loadPresets(false) // Don't create default preset to avoid infinite loop
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Preset created")
                }
            },
            onFailure = { e ->
                isLoading = false
                Log.e(TAG, "Error creating preset: ${e.message}", e)
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Failed to create preset: ${e.message}")
                }
            }
        )
    }

    // Function to delete a preset
    fun deletePreset(preset: VolunteerPreset) {
        isLoading = true

        // Delete the preset document
        FirebaseManager.getInstance().deleteDocument(
            "volunteerPresets/${preset.id}",
            onSuccess = {
                // Remove from local list
                presets = presets.filter { it.id != preset.id }
                isLoading = false

                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Preset deleted")
                }
            },
            onFailure = { e ->
                isLoading = false
                Log.e(TAG, "Error deleting preset: ${e.message}", e)
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Failed to delete preset: ${e.message}")
                }
            }
        )
    }

    // Function to rename a preset (requires creating new document with new name and deleting old one)
    fun renamePreset(preset: VolunteerPreset, newName: String) {
        if (newName.isBlank()) {
            coroutineScope.launch {
                snackbarHostState.showSnackbar("Preset name cannot be empty")
            }
            return
        }

        if (newName == preset.name) {
            // No change needed
            return
        }

        isLoading = true

        // First, get the current preset data
        FirebaseManager.getInstance().getDocument(
            "volunteerPresets/${preset.id}",
            onSuccess = { documentSnapshot ->
                val currentData = documentSnapshot.data?.toMutableMap() ?: mutableMapOf()

                // Update the data with new name and ID
                currentData["name"] = newName
                currentData["id"] = newName

                // Create new document with new name as ID
                FirebaseManager.getInstance().setDocument(
                    "volunteerPresets/$newName",
                    currentData,
                    onSuccess = {
                        // Delete the old document
                        FirebaseManager.getInstance().deleteDocument(
                            "volunteerPresets/${preset.id}",
                            onSuccess = {
                                isLoading = false
                                loadPresets(false) // Refresh the list
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("Preset renamed")
                                }
                            },
                            onFailure = { e ->
                                isLoading = false
                                Log.e(TAG, "Error deleting old preset: ${e.message}", e)
                                coroutineScope.launch {
                                    snackbarHostState.showSnackbar("Preset renamed but old version may still exist")
                                }
                            }
                        )
                    },
                    onFailure = { e ->
                        isLoading = false
                        Log.e(TAG, "Error creating renamed preset: ${e.message}", e)
                        coroutineScope.launch {
                            snackbarHostState.showSnackbar("Failed to rename preset: ${e.message}")
                        }
                    }
                )
            },
            onFailure = { e ->
                isLoading = false
                Log.e(TAG, "Error getting preset data: ${e.message}", e)
                coroutineScope.launch {
                    snackbarHostState.showSnackbar("Failed to rename preset: ${e.message}")
                }
            }
        )
    }

    // Trigger entrance animations with standardized UI.md timing
    LaunchedEffect(animationTrigger) {
        // Reset all animation states first
        topBarVisible = false
        contentVisible = false
        listVisible = false

        // Clear animated items tracking when screen is refreshed/revisited
        animatedItems.clear()

        if (animationsEnabled) {
            kotlinx.coroutines.delay(100)
            topBarVisible = true
            kotlinx.coroutines.delay(200)
            contentVisible = true
            kotlinx.coroutines.delay(100)
            listVisible = true
        } else {
            // Show all immediately if animations disabled
            topBarVisible = true
            contentVisible = true
            listVisible = true
        }
    }

    // Increment animation trigger when screen is visited to reset animations
    LaunchedEffect(Unit) {
        animationTrigger++
        loadPresets()
    }

    Scaffold(
        topBar = {
            // Animated top bar with standardized UI.md timing (500ms fadeIn + 100ms delay)
            AnimatedVisibility(
                visible = topBarVisible,
                enter = fadeIn(
                    animationSpec = tween(500, delayMillis = if (animationsEnabled) 100 else 0)
                ),
                exit = fadeOut(tween(200))
            ) {
                // Header layout with "New" button following UI.md header save button pattern
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, vertical = 16.dp), // UI.md header spacing
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Back button
                    IconButton(
                        onClick = { navController.navigateUp() },
                        modifier = Modifier.size(48.dp) // UI.md touch target
                    ) {
                        Icon(
                            imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                            contentDescription = "Back",
                            tint = Color.White,
                            modifier = Modifier.size(28.dp) // UI.md icon size
                        )
                    }

                    // Title
                    Text(
                        text = "Volunteer Presets",
                        style = MaterialTheme.typography.titleLarge.copy(fontSize = 22.sp),
                        color = Color.White,
                        fontWeight = FontWeight.Bold,
                        modifier = Modifier
                            .weight(1f)
                            .padding(start = 8.dp)
                    )

                    // "New" button - only show when data is loaded and not in loading state
                    if (!isLoading && (presets.isNotEmpty() || (!isLoading && presets.isEmpty()))) {
                        StandardButton(
                            onClick = {
                                newPresetName = ""
                                presetToRename = null
                                showRenameDialog = true
                            }
                        ) {
                            Text(
                                "New",
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }
            }
        },
        containerColor = Color.Transparent,
        contentWindowInsets = WindowInsets(0, 0, 0, 0),
        content = { paddingValues ->
            // Background gradient following UI.md specifications
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(DarkBackground)
                    .padding(paddingValues)
            ) {
                // Content with standardized UI.md timing (400ms slideIn + 300ms fadeIn)
                AnimatedVisibility(
                    visible = contentVisible,
                    enter = slideInVertically(
                        initialOffsetY = { if (animationsEnabled) it / 4 else 0 },
                        animationSpec = tween(durationMillis = if (animationsEnabled) 400 else 0)
                    ) + fadeIn(
                        animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
                    ),
                    exit = fadeOut(tween(200))
                ) {
                    Box(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(horizontal = 20.dp, vertical = 24.dp) // UI.md spacing
                    ) {
                        // Show loading indicator, error, or preset list with enhanced transitions
                        when {
                            isLoading -> {
                                // Enhanced loading animation with fade-in
                                AnimatedVisibility(
                                    visible = isLoading,
                                    enter = fadeIn(tween(300)) + scaleIn(
                                        initialScale = 0.8f,
                                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                                    ),
                                    exit = fadeOut(tween(200)) + scaleOut(
                                        targetScale = 0.8f,
                                        animationSpec = tween(200)
                                    )
                                ) {
                                    Box(
                                        modifier = Modifier.fillMaxSize(),
                                        contentAlignment = Alignment.Center
                                    ) {
                                        CircularProgressIndicator(
                                            color = YellowAccent,
                                            modifier = Modifier.size(48.dp),
                                            strokeWidth = 4.dp
                                        )
                                    }
                                }
                            }

                            errorMessage != null -> {
                                // Enhanced error state with slide-in animation
                                AnimatedVisibility(
                                    visible = errorMessage != null,
                                    enter = fadeIn(tween(300)) + slideInVertically(
                                        initialOffsetY = { it / 3 },
                                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                                    ) + scaleIn(
                                        initialScale = 0.9f,
                                        animationSpec = tween(300, easing = FastOutSlowInEasing)
                                    ),
                                    exit = fadeOut(tween(200)) + slideOutVertically(
                                        targetOffsetY = { -it / 3 },
                                        animationSpec = tween(200)
                                    )
                                ) {
                                    Card(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 16.dp),
                                        shape = RoundedCornerShape(16.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = DarkSurface
                                        ),
                                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                                    ) {
                                        Column(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(24.dp),
                                            horizontalAlignment = Alignment.CenterHorizontally
                                        ) {
                                            Text(
                                                text = errorMessage ?: "Unknown error",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = ErrorRed,
                                                textAlign = TextAlign.Center
                                            )

                                            Spacer(modifier = Modifier.height(24.dp))

                                            StandardButton(
                                                onClick = { loadPresets() }
                                            ) {
                                                Text(
                                                    "Try Again",
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            presets.isEmpty() -> {
                                // Enhanced empty state with slide-in animation
                                AnimatedVisibility(
                                    visible = presets.isEmpty() && !isLoading,
                                    enter = fadeIn(tween(300, delayMillis = 150)) + slideInVertically(
                                        initialOffsetY = { it / 3 },
                                        animationSpec = tween(300, delayMillis = 150, easing = FastOutSlowInEasing)
                                    ) + scaleIn(
                                        initialScale = 0.9f,
                                        animationSpec = tween(300, delayMillis = 150, easing = FastOutSlowInEasing)
                                    ),
                                    exit = fadeOut(tween(200))
                                ) {
                                    Card(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .padding(vertical = 16.dp),
                                        shape = RoundedCornerShape(16.dp),
                                        colors = CardDefaults.cardColors(
                                            containerColor = DarkSurface
                                        ),
                                        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
                                    ) {
                                        Column(
                                            modifier = Modifier
                                                .fillMaxWidth()
                                                .padding(24.dp),
                                            horizontalAlignment = Alignment.CenterHorizontally
                                        ) {
                                            Icon(
                                                imageVector = Icons.Default.Group,
                                                contentDescription = null,
                                                modifier = Modifier.size(72.dp),
                                                tint = NeutralGray
                                            )

                                            Spacer(modifier = Modifier.height(16.dp))

                                            Text(
                                                text = "No volunteer presets found",
                                                style = MaterialTheme.typography.titleMedium,
                                                color = Color.White,
                                                textAlign = TextAlign.Center,
                                                fontWeight = FontWeight.Medium
                                            )

                                            Spacer(modifier = Modifier.height(8.dp))

                                            Text(
                                                text = "Create your first volunteer preset to get started",
                                                style = MaterialTheme.typography.bodyMedium,
                                                color = Color(0xFFB0B0B0), // Secondary text color from UI.md
                                                textAlign = TextAlign.Center
                                            )

                                            Spacer(modifier = Modifier.height(24.dp))

                                            StandardButton(
                                                onClick = {
                                                    newPresetName = ""
                                                    presetToRename = null
                                                    showRenameDialog = true
                                                }
                                            ) {
                                                Icon(
                                                    Icons.Default.Add,
                                                    contentDescription = null,
                                                    modifier = Modifier.size(20.dp)
                                                )
                                                Spacer(modifier = Modifier.width(8.dp))
                                                Text(
                                                    "Create New Preset",
                                                    fontWeight = FontWeight.Medium
                                                )
                                            }
                                        }
                                    }
                                }
                            }

                            else -> {
                                // Animated list with standardized UI.md timing (300ms slideIn + 250ms fadeIn)
                                AnimatedVisibility(
                                    visible = listVisible,
                                    enter = slideInVertically(
                                        initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
                                        animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
                                    ) + fadeIn(
                                        animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
                                    ),
                                    exit = fadeOut(tween(200)) + slideOutVertically(
                                        targetOffsetY = { -it / 3 },
                                        animationSpec = tween(200)
                                    )
                                ) {
                                    LazyColumn(
                                        modifier = Modifier.fillMaxSize(),
                                        verticalArrangement = Arrangement.spacedBy(16.dp), // UI.md card spacing
                                        contentPadding = PaddingValues(bottom = 100.dp) // UI.md bottom padding to prevent navigation bar overlap
                                    ) {
                                        itemsIndexed(presets) { index, preset ->
                                            // Enhanced staggered animation following UI.md pattern
                                            // Base delay + additional staggering for slower, more gradual appearance
                                            val baseDelay = if (animationsEnabled) 200 else 0 // Additional base delay
                                            val itemDelay = if (animationsEnabled) baseDelay + (index * 150).coerceAtMost(800) else 0

                                            // Individual item animation state
                                            val itemVisible = remember { mutableStateOf(false) }

                                            // Check if this item has already been animated
                                            val hasBeenAnimated = animatedItems.contains(preset.id)

                                            LaunchedEffect(preset.id, listVisible, animationTrigger) {
                                                // Only animate if item hasn't been animated before and list is visible
                                                if (!hasBeenAnimated && listVisible) {
                                                    // Reset animation state first
                                                    itemVisible.value = false

                                                    if (animationsEnabled) {
                                                        kotlinx.coroutines.delay(itemDelay.toLong())
                                                    }
                                                    itemVisible.value = true

                                                    // Mark this item as animated
                                                    animatedItems.add(preset.id)
                                                } else if (hasBeenAnimated) {
                                                    // Item already animated, show immediately
                                                    itemVisible.value = true
                                                }
                                            }

                                            AnimatedVisibility(
                                                visible = itemVisible.value,
                                                enter = slideInVertically(
                                                    initialOffsetY = { if (animationsEnabled) it / 2 else 0 },
                                                    animationSpec = tween(durationMillis = if (animationsEnabled) 300 else 0)
                                                ) + fadeIn(
                                                    animationSpec = tween(durationMillis = if (animationsEnabled) 250 else 0)
                                                ),
                                                exit = fadeOut(tween(200)) + slideOutVertically(
                                                    targetOffsetY = { it / 2 },
                                                    animationSpec = tween(200)
                                                )
                                            ) {
                                                PresetCard(
                                                    preset = preset,
                                                    onEdit = { navController.navigate("manageVolunteers/${preset.id}") },
                                                    onRename = {
                                                        presetToRename = preset
                                                        newPresetName = preset.name
                                                        showRenameDialog = true
                                                    },
                                                    onDelete = {
                                                        presetToDelete = preset
                                                        showDeleteDialog = true
                                                    }
                                                )
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        },
        snackbarHost = { SnackbarHost(snackbarHostState) }
    )

    // Create New Preset dialog with UI.md specifications
    if (showRenameDialog) {
        AlertDialog(
            onDismissRequest = {
                showRenameDialog = false
                presetToRename = null
            },
            title = {
                Text(
                    text = if (presetToRename == null) "Create New Preset" else "Rename Preset",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold
                )
            },
            containerColor = NeutralCardSurface, // UI.md dialog background
            titleContentColor = Color.White,
            textContentColor = Color.White,
            shape = RoundedCornerShape(16.dp), // UI.md dialog corner radius
            text = {
                OutlinedTextField(
                    value = newPresetName,
                    onValueChange = { newPresetName = it },
                    label = { Text("Preset Name") },
                    singleLine = true,
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 56.dp), // UI.md minimum height
                    colors = TextFieldDefaults.outlinedTextFieldColors(
                        focusedBorderColor = YellowAccent, // UI.md focused color
                        unfocusedBorderColor = NeutralGray.copy(alpha = 0.7f), // UI.md unfocused color
                        focusedLabelColor = YellowAccent,
                        unfocusedLabelColor = NeutralGray,
                        cursorColor = YellowAccent,
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White
                    ),
                    shape = RoundedCornerShape(12.dp) // UI.md input field corner radius
                )
            },
            confirmButton = {
                StandardButton(
                    onClick = {
                        if (presetToRename != null) {
                            renamePreset(presetToRename!!, newPresetName)
                        } else {
                            createNewPreset(newPresetName)
                        }
                        showRenameDialog = false
                        newPresetName = ""
                        presetToRename = null
                    }
                ) {
                    Text(
                        if (presetToRename == null) "Create" else "Rename",
                        fontWeight = FontWeight.Medium
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showRenameDialog = false
                        presetToRename = null
                        newPresetName = ""
                    }
                ) {
                    Text("Cancel", color = YellowAccent) // UI.md cancel button color
                }
            }
        )
    }

    // Delete confirmation dialog with UI.md specifications
    if (showDeleteDialog && presetToDelete != null) {
        AlertDialog(
            onDismissRequest = {
                showDeleteDialog = false
                presetToDelete = null
            },
            title = {
                Text(
                    "Delete Preset",
                    style = MaterialTheme.typography.titleMedium,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
            },
            containerColor = DarkSurface, // UI.md dialog background
            titleContentColor = Color.White,
            textContentColor = Color.White,
            shape = RoundedCornerShape(16.dp), // UI.md dialog corner radius
            text = {
                Text(
                    "Are you sure you want to delete '${presetToDelete!!.name}'? This action cannot be undone.",
                    style = MaterialTheme.typography.bodyMedium,
                    color = Color(0xFFB0B0B0) // UI.md secondary text color
                )
            },
            confirmButton = {
                Button(
                    onClick = {
                        deletePreset(presetToDelete!!)
                        showDeleteDialog = false
                        presetToDelete = null
                    },
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ErrorRed // UI.md error color
                    ),
                    shape = RoundedCornerShape(8.dp) // UI.md button corner radius
                ) {
                    Text(
                        "Delete",
                        color = Color.White,
                        fontWeight = FontWeight.Medium
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        presetToDelete = null
                    }
                ) {
                    Text("Cancel", color = YellowAccent) // UI.md cancel button color
                }
            }
        )
    }
}

@Composable
fun GroupChipsFlowLayout(
    groups: List<Pair<String, Int>>,
    modifier: Modifier = Modifier
) {
    // Hard limit of 5 groups per line
    val maxGroupsPerLine = 5

    // Split the groups into chunks of 5
    val chunkedGroups = groups.chunked(maxGroupsPerLine)

    Column(modifier = modifier) {
        chunkedGroups.forEach { lineGroups ->
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                lineGroups.forEach { (group, count) ->
                    GroupChip(group = group, count = count)
                }
            }

            if (chunkedGroups.last() != lineGroups) {
                Spacer(modifier = Modifier.height(6.dp))
            }
        }
    }
}

@Composable
fun PresetCard(
    preset: VolunteerPreset,
    onEdit: () -> Unit,
    onRename: () -> Unit,
    onDelete: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onEdit() },
        shape = RoundedCornerShape(16.dp), // UI.md corner radius
        colors = CardDefaults.cardColors(
            containerColor = NeutralCardSurface // UI.md preferred card background
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 4.dp) // UI.md elevation
    ) {
        Column(
            modifier = Modifier.padding(20.dp) // UI.md card padding
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                // Clickable preset name for rename functionality
                Text(
                    text = preset.name,
                    style = MaterialTheme.typography.titleMedium,
                    color = Color.White,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .weight(1f)
                        .clickable { onRename() }
                        .padding(vertical = 8.dp) // Add padding for better touch target
                )

                // Delete button with UI.md small icon specifications (36dp container, 18dp icon) to match TeachingSlotScreen
                IconButton(
                    onClick = onDelete,
                    modifier = Modifier
                        .size(36.dp)
                        .background(
                            color = Color.Red.copy(alpha = 0.2f),
                            shape = CircleShape
                        )
                ) {
                    Icon(
                        imageVector = Icons.Default.Delete,
                        contentDescription = "Delete",
                        tint = Color(0xFFFF5252), // Red color for delete
                        modifier = Modifier.size(18.dp)
                    )
                }
            }

            Spacer(modifier = Modifier.height(8.dp)) // UI.md element spacing

            // Show volunteer count as a detail line with UI.md secondary text color
            Text(
                text = "${preset.volunteerCount} volunteer${if (preset.volunteerCount != 1) "s" else ""}",
                style = MaterialTheme.typography.bodyMedium,
                color = Color(0xFFB0B0B0) // UI.md secondary text color
            )

            // Show volunteer distribution by academic group
            if (preset.groupCounts.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp)) // UI.md element spacing

                // Filter out groups with zero volunteers and sort numerically
                val nonZeroGroups = preset.groupCounts.entries
                    .filter { it.value > 0 }
                    .sortedBy { it.key.toIntOrNull() ?: Int.MAX_VALUE }
                    .map { it.key to it.value }

                if (nonZeroGroups.isNotEmpty()) {
                    GroupChipsFlowLayout(groups = nonZeroGroups)
                }
            }
        }
    }
}

@Composable
fun GroupChip(group: String, count: Int) {
    val chipText = "Gp $group: $count"

    Box(
        modifier = Modifier
            .height(28.dp)
            .defaultMinSize(minWidth = 60.dp) // Ensure minimum width for all chips
            .background(
                color = YellowAccent, // UI.md primary accent color
                shape = RoundedCornerShape(4.dp)
            )
            .padding(horizontal = 6.dp, vertical = 4.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = chipText,
            style = MaterialTheme.typography.bodySmall.copy(
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium
            ),
            color = Color.Black, // Black text on yellow background per UI.md
            maxLines = 1,
            overflow = TextOverflow.Visible // Ensure text is not cut off
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun VolunteerPresetsScreenPreview() {
    MaterialTheme {
        VolunteerPresetsScreen(rememberNavController())
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun PresetCardPreview() {
    MaterialTheme {
        PresetCard(
            preset = VolunteerPreset(
                id = "1",
                name = "Morning Volunteers",
                createdAt = System.currentTimeMillis(),
                volunteerCount = 25,
                groupCounts = mapOf(
                    "1" to 5,
                    "2" to 8,
                    "3" to 12
                )
            ),
            onEdit = {},
            onRename = {},
            onDelete = {}
        )
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun GroupChipPreview() {
    MaterialTheme {
        GroupChip(group = "3", count = 12)
    }
}