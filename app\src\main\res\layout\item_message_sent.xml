<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="8dp"
    android:paddingBottom="8dp">

    <!-- Mention badge (small colored indicator) -->
    <View
        android:id="@+id/mention_badge"
        android:layout_width="6dp"
        android:layout_height="0dp"
        android:background="#4CAF50"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/message_card"
        app:layout_constraintEnd_toStartOf="@+id/message_card"
        app:layout_constraintTop_toTopOf="@+id/message_card" />

    <!-- Message card -->
    <androidx.cardview.widget.CardView
        android:id="@+id/message_card"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:maxWidth="260dp"
        app:cardBackgroundColor="#006BFF"
        app:cardCornerRadius="12dp"
        app:cardElevation="1dp"
        app:layout_constrainedWidth="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_max="wrap">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- Message text -->
            <TextView
                android:id="@+id/text_message_body"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="#FFFFFF"
                android:textSize="16sp" />
                
            <!-- Image content for media messages -->
            <ImageView
                android:id="@+id/image_message_content"
                android:layout_width="200dp"
                android:layout_height="200dp"
                android:layout_marginTop="4dp"
                android:adjustViewBounds="true"
                android:scaleType="centerCrop"
                android:visibility="gone"
                android:contentDescription="Image message" />

            <!-- Message time -->
            <TextView
                android:id="@+id/text_message_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="4dp"
                android:textColor="#E6FFFFFF"
                android:textSize="12sp" />

            <!-- Message status (read/delivered) -->
            <TextView
                android:id="@+id/text_message_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginTop="2dp"
                android:text="Delivered"
                android:textColor="#E6FFFFFF"
                android:textSize="10sp" />

        </LinearLayout>
    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout> 