package com.phad.chatapp.fragments

import android.app.Activity
import android.app.Dialog
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.Button
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.Query
import com.phad.chatapp.ChatActivity
import com.phad.chatapp.MainActivity
import com.phad.chatapp.R
import com.phad.chatapp.utils.DriveServiceHelper
import com.phad.chatapp.utils.SessionManager
import com.phad.chatapp.adapters.UpdateCardAdapter
import com.phad.chatapp.databinding.FragmentHomeBinding
import com.phad.chatapp.models.Update
import java.io.FileNotFoundException
import java.util.Calendar
import java.util.UUID
import com.google.firebase.firestore.FieldValue
import com.phad.chatapp.utils.NotificationHelper
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

class HomeFragment : Fragment() {
    private val TAG = "HomeFragment"
    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!
    
    private lateinit var updateCardAdapter: UpdateCardAdapter
    private val updatesList = mutableListOf<Update>()
    private lateinit var sessionManager: SessionManager
    private val db = FirebaseFirestore.getInstance()
    private val auth = FirebaseAuth.getInstance()
    private lateinit var driveServiceHelper: DriveServiceHelper
    
    // Create update dialog
    private lateinit var createUpdateDialog: Dialog
    private var selectedImageUri: Uri? = null
    private var selectedDocumentUri: Uri? = null
    
    // Image picker launcher
    private val imagePicker = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                selectedImageUri = uri
                showSelectedImage(uri)
            }
        }
    }
    
    // Document picker launcher
    private val documentPicker = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                selectedDocumentUri = uri
                showSelectedDocument(uri)
            }
        }
    }
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        sessionManager = SessionManager(requireContext())
        driveServiceHelper = DriveServiceHelper.getInstance(requireContext())
        
        setupGreeting()
        setupUpdatesList()
        setupBottomNavigation()
        setupHeaderIcons()
        setupAddUpdateButton()
        loadNextClass()
        loadUpdates()
    }
    
    private fun setupGreeting() {
        // Set greeting based on time of day
        val greeting = getGreetingBasedOnTime()
        binding.greetingTextView.text = greeting
        
        // Get user name from SessionManager - Show full name
        val userName = sessionManager.fetchUserName() ?: "User"
        binding.userNameTextView.text = userName
    }
    
    private fun getGreetingBasedOnTime(): String {
        val calendar = Calendar.getInstance()
        return when (calendar.get(Calendar.HOUR_OF_DAY)) {
            in 0..11 -> "Good Morning"
            in 12..16 -> "Good Afternoon"
            else -> "Good Evening"
        }
    }
    
    private fun loadNextClass() {
        // In a real app, this would fetch the next class info from a database or API
        // For now, we'll just display static content
        binding.nextClassDateTimeTextView.text = "24 April, 3:00 PM"
        binding.nextClassLocationTextView.text = "in Raghunpur"
    }
    
    private fun setupUpdatesList() {
        Log.d(TAG, "Setting up updates RecyclerView")
        
        // Setup adapter
        updateCardAdapter = UpdateCardAdapter()
        
        // Set up click listener for updates
        updateCardAdapter.setOnUpdateClickListener(object : UpdateCardAdapter.OnUpdateClickListener {
            override fun onUpdateClick(update: Update) {
                // Open the detail activity when an update is clicked
                val intent = Intent(requireContext(), com.phad.chatapp.activities.UpdateDetailActivity::class.java)
                intent.putExtra(com.phad.chatapp.activities.UpdateDetailActivity.EXTRA_UPDATE, update)
                startActivity(intent)
            }
        })
        
        // Use proper layout manager with correct parameters
        val layoutManager = LinearLayoutManager(
            requireContext(), 
            LinearLayoutManager.HORIZONTAL, 
            false
        )
        
        // Configure RecyclerView
        binding.updatesRecyclerView.apply {
            this.layoutManager = layoutManager
            adapter = updateCardAdapter
            setHasFixedSize(true)
            clipToPadding = false // Allow items to draw beyond padding
            visibility = View.VISIBLE
            
            // Add decoration for spacing between items if needed
            val spacingInPixels = resources.getDimensionPixelSize(R.dimen.card_spacing)
            if (itemDecorationCount == 0) {
                addItemDecoration(object : RecyclerView.ItemDecoration() {
                    override fun getItemOffsets(
                        outRect: android.graphics.Rect,
                        view: View,
                        parent: RecyclerView,
                        state: RecyclerView.State
                    ) {
                        outRect.right = spacingInPixels
                        // Add left padding only for the first item
                        if (parent.getChildAdapterPosition(view) == 0) {
                            outRect.left = spacingInPixels / 2
                        }
                    }
                })
            }
        }
        
        // Debug log to check RecyclerView configuration
        Log.d(TAG, "RecyclerView configured with horizontal layout manager. Adapter item count: ${updateCardAdapter.itemCount}")
    }
    
    private fun setupBottomNavigation() {
        // Hide the redundant bottom navigation in the fragment
        binding.bottomNavigation.visibility = View.GONE
    }
    
    private fun setupHeaderIcons() {
        // Update the icons to use the new drawables
        binding.todoButton.setImageResource(R.drawable.ic_todo)
        binding.chatbotButton.setImageResource(R.drawable.ic_chatbot)
        
        // Setup todo icon click
        binding.todoButton.setOnClickListener {
            // Navigate to TodoFragment
            val todoFragment = com.phad.chatapp.fragments.TodoFragment.newInstance()
            requireActivity().supportFragmentManager.beginTransaction()
                .replace(R.id.content_frame, todoFragment)
                .addToBackStack(null)
                .commit()
        }
        
        // Setup chatbot icon click
        binding.chatbotButton.setOnClickListener {
            // Launch the ChatBotActivity
            val intent = Intent(requireContext(), com.phad.chatapp.features.home.chatbot.ui.ChatBotActivity::class.java)
            startActivity(intent)
        }
    }
    
    private fun setupAddUpdateButton() {
        // Only show add update button if user is admin
        val userType = sessionManager.fetchUserType()
        
        // Show button for admin users
        val isAdmin = userType == "Admin1" || userType == "Admin2" || 
                     userType.contains("Admin", ignoreCase = true)
                      
        binding.addUpdateFab.visibility = if (isAdmin) View.VISIBLE else View.GONE
        
        binding.addUpdateFab.setOnClickListener {
            showCreateUpdateDialog()
        }
    }
    
    private fun showCreateUpdateDialog() {
        // Initialize the dialog
        createUpdateDialog = Dialog(requireContext())
        createUpdateDialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        createUpdateDialog.setContentView(R.layout.dialog_create_update)
        createUpdateDialog.window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        
        // Log auth status and user information for debugging
        val currentUser = auth.currentUser
        if (currentUser != null) {
            Log.d(TAG, "User is authenticated: ${currentUser.uid}, Email: ${currentUser.email}")
        } else {
            Log.e(TAG, "User is not authenticated. This may cause Google Drive upload failures.")
        }
        
        // Reset selected media
        selectedImageUri = null
        selectedDocumentUri = null
        
        // Find views in the dialog
        val updateContentInput = createUpdateDialog.findViewById<EditText>(R.id.updateContentInput)
        val updateTitleInput = createUpdateDialog.findViewById<EditText>(R.id.updateTitleInput)
        val updateLinkInput = createUpdateDialog.findViewById<EditText>(R.id.updateLinkInput)
        val attachImageButton = createUpdateDialog.findViewById<ImageButton>(R.id.attachImageButton)
        val attachDocumentButton = createUpdateDialog.findViewById<ImageButton>(R.id.attachDocumentButton)
        val cancelButton = createUpdateDialog.findViewById<Button>(R.id.cancelButton)
        val postUpdateButton = createUpdateDialog.findViewById<Button>(R.id.postUpdateButton)
        
        // Hide preview containers initially
        val mediaPreviewContainer = createUpdateDialog.findViewById<FrameLayout>(R.id.mediaPreviewContainer)
        val documentPreviewContainer = createUpdateDialog.findViewById<LinearLayout>(R.id.documentPreviewContainer)
        mediaPreviewContainer.visibility = View.GONE
        documentPreviewContainer.visibility = View.GONE
        
        // Set up attach image button
        attachImageButton.setOnClickListener {
            val intent = Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI)
            imagePicker.launch(intent)
        }
        
        // Set up attach document button
        attachDocumentButton.setOnClickListener {
            val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
                addCategory(Intent.CATEGORY_OPENABLE)
                type = "*/*"
            }
            documentPicker.launch(intent)
        }
        
        // Set up remove media button
        val removeMediaButton = createUpdateDialog.findViewById<ImageButton>(R.id.removeMediaButton)
        removeMediaButton.setOnClickListener {
            selectedImageUri = null
            mediaPreviewContainer.visibility = View.GONE
        }
        
        // Set up remove document button
        val removeDocumentButton = createUpdateDialog.findViewById<ImageButton>(R.id.removeDocumentButton)
        removeDocumentButton.setOnClickListener {
            selectedDocumentUri = null
            documentPreviewContainer.visibility = View.GONE
        }
        
        // Set up cancel button
        cancelButton.setOnClickListener {
            createUpdateDialog.dismiss()
        }
        
        // Set up post update button
        postUpdateButton.setOnClickListener {
            val content = updateContentInput.text.toString().trim()
            if (content.isEmpty()) {
                Toast.makeText(requireContext(), "Please enter update content", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }
            
            // Show loading indicator
            createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = false
            createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = false
            
            // Create update data
            val title = updateTitleInput.text.toString().trim()
            val link = updateLinkInput.text.toString().trim()
            
            // Upload media if selected
            if (selectedImageUri != null) {
                uploadMedia(selectedImageUri!!) { mediaUrl: String ->
                    // Upload document if selected
                    if (selectedDocumentUri != null) {
                        uploadDocument(selectedDocumentUri!!) { documentUrl: String, documentName: String ->
                            createUpdate(content, title, link, mediaUrl, documentUrl, documentName)
                        }
                    } else {
                        createUpdate(content, title, link, mediaUrl, null, null)
                    }
                }
            } else if (selectedDocumentUri != null) {
                uploadDocument(selectedDocumentUri!!) { documentUrl: String, documentName: String ->
                    createUpdate(content, title, link, null, documentUrl, documentName)
                }
            } else {
                createUpdate(content, title, link, null, null, null)
            }
        }
        
        createUpdateDialog.show()
    }
    
    private fun showSelectedImage(uri: Uri) {
        val mediaPreviewContainer = createUpdateDialog.findViewById<FrameLayout>(R.id.mediaPreviewContainer)
        val mediaPreview = createUpdateDialog.findViewById<ImageView>(R.id.mediaPreview)
        
        mediaPreviewContainer.visibility = View.VISIBLE
        
        try {
            Glide.with(requireContext())
                .load(uri)
                .centerCrop()
                .into(mediaPreview)
        } catch (e: FileNotFoundException) {
            Toast.makeText(requireContext(), "Failed to load image", Toast.LENGTH_SHORT).show()
            mediaPreviewContainer.visibility = View.GONE
        }
    }
    
    private fun showSelectedDocument(uri: Uri) {
        val documentPreviewContainer = createUpdateDialog.findViewById<LinearLayout>(R.id.documentPreviewContainer)
        val documentNameText = createUpdateDialog.findViewById<TextView>(R.id.documentNameText)
        
        // Get document name from URI
        val documentName = getDocumentName(uri)
        documentNameText.text = documentName
        
        documentPreviewContainer.visibility = View.VISIBLE
    }
    
    private fun getDocumentName(uri: Uri): String {
        var fileName = "document"
        
        context?.contentResolver?.query(uri, null, null, null, null)?.use { cursor ->
            if (cursor.moveToFirst()) {
                val displayNameIndex = cursor.getColumnIndex(MediaStore.MediaColumns.DISPLAY_NAME)
                if (displayNameIndex != -1) {
                    fileName = cursor.getString(displayNameIndex)
                }
            }
        }
        
        return fileName
    }
    
    private fun uploadMedia(uri: Uri, onComplete: (String) -> Unit) {
        try {
            // Validate the URI is accessible
            val inputStream = requireContext().contentResolver.openInputStream(uri)
            inputStream?.close()
            
            // Show a loading indication
            Toast.makeText(requireContext(), "Uploading image...", Toast.LENGTH_SHORT).show()
            
            // Generate a unique filename for the image
            val filename = "update_image_${System.currentTimeMillis()}_${UUID.randomUUID()}.jpg"
            
            // Log the upload attempt for debugging
            Log.d(TAG, "Uploading image to Google Drive: $filename")
            
            // Upload to Google Drive instead of Firebase Storage
            driveServiceHelper.uploadFileToDrive(uri, filename, "image/jpeg") { success, driveFileId, webViewLink ->
                if (success && webViewLink != null) {
                    // Convert to a direct media URL for better Glide compatibility
                    val directMediaUrl = driveServiceHelper.getDirectMediaUrl(webViewLink, true)
                    
                    Log.d(TAG, "Upload completed. Drive ID: $driveFileId")
                    Log.d(TAG, "Original URL: $webViewLink")
                    Log.d(TAG, "Direct media URL: $directMediaUrl")
                    
                    onComplete(directMediaUrl)
                } else {
                    val errorMsg = "Failed to upload to Google Drive"
                    Log.e(TAG, errorMsg)
                    requireActivity().runOnUiThread {
                        Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                        createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = true
                        createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = true
                    }
                }
            }
        } catch (e: Exception) {
            val errorMsg = e.localizedMessage ?: "Unknown error"
            Log.e(TAG, "Failed to access file: $errorMsg", e)
            Toast.makeText(requireContext(), "Error accessing the file: $errorMsg", Toast.LENGTH_SHORT).show()
            createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = true
            createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = true
        }
    }
    
    private fun uploadDocument(uri: Uri, onComplete: (String, String) -> Unit) {
        try {
            // Validate the URI is accessible
            val inputStream = requireContext().contentResolver.openInputStream(uri)
            inputStream?.close()
            
            // Show a loading indication
            Toast.makeText(requireContext(), "Uploading document...", Toast.LENGTH_SHORT).show()
            
            // Get document name from URI
            val documentName = getDocumentName(uri)
            
            // Get MIME type of the document
            val mimeType = requireContext().contentResolver.getType(uri) ?: "application/octet-stream"
            
            // Generate a unique filename for the document 
            val filename = "update_doc_${System.currentTimeMillis()}_${UUID.randomUUID()}_$documentName"
            
            // Log the upload attempt for debugging
            Log.d(TAG, "Uploading document to Google Drive: $filename (${mimeType})")
            
            // Upload to Google Drive instead of Firebase Storage
            driveServiceHelper.uploadFileToDrive(uri, filename, mimeType) { success, driveFileId, webViewLink ->
                if (success && driveFileId != null) {
                    // Use the standard file view URL format that works without Google auth
                    // This is the format that's working in the group chat
                    val directFileUrl = "https://drive.google.com/file/d/${driveFileId}/view?usp=sharing"
                    
                    Log.d(TAG, "Upload completed. Drive ID: $driveFileId")
                    Log.d(TAG, "Original URL: $webViewLink")
                    Log.d(TAG, "Direct file URL: $directFileUrl")
                    
                    onComplete(directFileUrl, documentName)
                } else {
                    val errorMsg = "Failed to upload to Google Drive"
                    Log.e(TAG, errorMsg)
                    requireActivity().runOnUiThread {
                        Toast.makeText(requireContext(), errorMsg, Toast.LENGTH_SHORT).show()
                        createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = true
                        createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = true
                    }
                }
            }
        } catch (e: Exception) {
            val errorMsg = e.localizedMessage ?: "Unknown error"
            Log.e(TAG, "Failed to access file: $errorMsg", e)
            Toast.makeText(requireContext(), "Error accessing the file: $errorMsg", Toast.LENGTH_SHORT).show()
            createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = true
            createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = true
        }
    }
    
    private fun createUpdate(
        content: String,
        title: String?,
        link: String?,
        mediaUrl: String?,
        documentUrl: String?,
        documentName: String?
    ) {
        val userId = auth.currentUser?.uid ?: return
        val authorName = sessionManager.fetchUserName() ?: "Admin"
        val authorImageUrl = auth.currentUser?.photoUrl?.toString()
        
        // Generate a timestamp for the update
        val timestamp = System.currentTimeMillis()
        
        // Get the user's roll number - use a default if not available
        val userRollNumber = sessionManager.fetchRollNumber() ?: userId.takeLast(6)
        
        // Extract only the numeric part of the roll number (removing any non-digit characters)
        val numericRollNumber = userRollNumber.filter { it.isDigit() }
        
        // Create a custom document ID combining timestamp and roll number (without underscore)
        val customDocId = "${timestamp}${numericRollNumber}"
        
        // Create the update object
        val update = Update(
            id = customDocId, // Use the custom ID
            authorId = userRollNumber, // Use actual roll number as authorId, NOT the Firebase UID
            authorName = authorName,
            authorImageUrl = authorImageUrl,
            title = if (title.isNullOrEmpty()) null else title,
            content = content,
            mediaUrl = mediaUrl,
            documentUrl = documentUrl,
            documentName = documentName,
            url = if (link.isNullOrEmpty()) null else link,
            timestamp = timestamp
        )
        
        // Save to Firestore using the custom document ID
        db.collection("updates").document(customDocId)
            .set(update)
            .addOnSuccessListener { 
                Toast.makeText(requireContext(), "Update posted", Toast.LENGTH_SHORT).show()
                createUpdateDialog.dismiss()
                
                // Reload updates
                loadUpdates()
                
                // Send notification to all users
                sendUpdateNotification(update)
            }
            .addOnFailureListener { e ->
                Toast.makeText(requireContext(), "Failed to post update: ${e.localizedMessage}", Toast.LENGTH_SHORT).show()
                createUpdateDialog.findViewById<Button>(R.id.postUpdateButton).isEnabled = true
                createUpdateDialog.findViewById<Button>(R.id.cancelButton).isEnabled = true
            }
    }
    
    private fun sendUpdateNotification(update: Update) {
        Log.d(TAG, "Preparing to send update notification for update: ${update.id}")
        
        try {
            // Use the NotificationHelper class that's already used for chat notifications
            val notificationHelper = NotificationHelper(requireContext())
            
            // Create a descriptive message that includes update info
            val title = update.title ?: "New Update"
            val message = "${title}: ${update.content.take(100)}${if (update.content.length > 100) "..." else ""}"
            
            // If there's a document, mention it in the notification
            val fullMessage = if (update.documentUrl != null) {
                "$message [Contains document]"
            } else {
                message
            }
            
            Log.d(TAG, "Update notification message: $fullMessage")
            
            // Get all users to notify
            val db = FirebaseFirestore.getInstance()
            
            db.collection("users")
                .get()
                .addOnSuccessListener { usersSnapshot ->
                    // Get all user IDs to notify
                    val allUserIds = usersSnapshot.documents.map { it.id }
                    
                    Log.d(TAG, "Found ${allUserIds.size} users to notify about update ${update.id}")
                    
                    // Launch a coroutine to call the suspend function
                    CoroutineScope(Dispatchers.Main).launch {
                        try {
                            Log.d(TAG, "Launching coroutine to send update notification")
                            
                            // Create and send notification with all users as participants
                            notificationHelper.sendUpdateNotification(
                                updateId = update.id,
                                updateTitle = title,
                                updateMessage = fullMessage,
                                senderRollNumber = update.authorId,
                                senderName = update.authorName,
                                allUserIds = allUserIds
                            )
                            
                            Log.d(TAG, "Update notification successfully sent via NotificationHelper")
                        } catch (e: Exception) {
                            Log.e(TAG, "Error in coroutine sending notification: ${e.message}", e)
                        }
                    }
                }
                .addOnFailureListener { e ->
                    Log.e(TAG, "Error fetching users for notification: ${e.message}", e)
                    Toast.makeText(
                        requireContext(),
                        "Failed to send notifications: ${e.localizedMessage}",
                        Toast.LENGTH_SHORT
                    ).show()
                }
            
        } catch (e: Exception) {
            Log.e(TAG, "Error creating update notification: ${e.message}", e)
            Toast.makeText(
                requireContext(),
                "Failed to send notifications: ${e.localizedMessage}",
                Toast.LENGTH_SHORT
            ).show()
        }
    }
    
    private fun loadUpdates() {
        // Don't proceed if the view has been destroyed
        if (_binding == null) return
        
        binding.updatesProgressBar.visibility = View.VISIBLE
        binding.noUpdatesTextView.visibility = View.GONE
        
        Log.d(TAG, "Loading updates from Firestore...")
        
        // Add a test update if the list is empty (for testing only)
        val testUpdate = Update(
            id = "test_update_${System.currentTimeMillis()}",
            authorId = "test_author",
            authorName = "Test Author",
            title = "Test Update",
            content = "This is a test update to confirm the RecyclerView is working",
            timestamp = System.currentTimeMillis()
        )
        
        // First attempt to read updates directly
        db.collection("updates")
            .get()
            .addOnSuccessListener { snapshot ->
                Log.d(TAG, "Direct query found ${snapshot.size()} documents")
                
                // Check document structure
                for (doc in snapshot.documents) {
                    Log.d(TAG, "Document ID: ${doc.id}, Data: ${doc.data}")
                }
            }
            .addOnFailureListener { e ->
                Log.e(TAG, "Direct query failed: ${e.message}")
            }
        
        // Now perform the actual query with ordering
        val updateTask = db.collection("updates")
            .orderBy("timestamp", Query.Direction.DESCENDING)
            .limit(10)
            .get()
            
        updateTask.addOnSuccessListener { result ->
            // Check if fragment is still attached and binding is valid
            if (_binding == null || !isAdded) return@addOnSuccessListener
            
            binding.updatesProgressBar.visibility = View.GONE
            updatesList.clear()
            
            Log.d(TAG, "Found ${result.size()} updates in Firestore")
            
            // If no real updates found, add test update for debugging
            if (result.isEmpty) {
                Log.d(TAG, "No updates found, adding test update for debugging")
                updatesList.add(testUpdate)
            } else {
            for (document in result) {
                try {
                        Log.d(TAG, "Processing update document: ${document.id}")
                        Log.d(TAG, "Document data: ${document.data}")
                        
                    val update = document.toObject(Update::class.java)
                        
                        // Ensure ID is set if missing
                        if (update.id.isEmpty()) {
                            val updatedUpdate = update.copy(id = document.id)
                            updatesList.add(updatedUpdate)
                        } else {
                    updatesList.add(update)
                        }
                        
                        // Log the update details
                        Log.d(TAG, "Parsed update: id=${update.id}, title=${update.title}, authorName=${update.authorName}")
                        
                } catch (e: Exception) {
                        Log.e(TAG, "Error parsing update: ${e.localizedMessage}", e)
                    }
                }
            }
            
            // Another check before updating adapter
            if (_binding == null || !isAdded) return@addOnSuccessListener
            
            Log.d(TAG, "Updating adapter with ${updatesList.size} updates")
            
            // Force layout refresh
            binding.updatesRecyclerView.visibility = View.GONE
            binding.updatesRecyclerView.visibility = View.VISIBLE
            
            // Update adapter with the list
            updateCardAdapter.submitList(null) // Clear the list first
            updateCardAdapter.submitList(updatesList) // Then submit the new list
            
            // Show no updates message if list is empty
            if (updatesList.isEmpty()) {
                binding.noUpdatesTextView.visibility = View.VISIBLE
                Log.d(TAG, "No updates found - showing empty message")
            } else {
                binding.noUpdatesTextView.visibility = View.GONE
                
                // Ensure RecyclerView is visible and has the right orientation
                binding.updatesRecyclerView.visibility = View.VISIBLE
            }
        }
        
        updateTask.addOnFailureListener { e ->
            // Check if fragment is still attached and binding is valid
            if (_binding == null || !isAdded) return@addOnFailureListener
            
            binding.updatesProgressBar.visibility = View.GONE
            binding.noUpdatesTextView.visibility = View.VISIBLE
            Log.e(TAG, "Error loading updates: ${e.localizedMessage}", e)
            
            // For testing: Add test update if loading fails
            updatesList.clear()
            updatesList.add(testUpdate)
            updateCardAdapter.submitList(updatesList)
            
            // Check if context is still available
            val context = context
            if (context != null) {
                Toast.makeText(context, "Failed to load updates", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        // Only reload updates if the view exists
        if (_binding != null) {
            loadUpdates()
        }
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
} 