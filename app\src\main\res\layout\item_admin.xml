<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="4dp"
    app:cardBackgroundColor="#E8EAF6">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="12dp">

        <View
            android:id="@+id/view_important_indicator"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/dot_important"
            android:visibility="gone" />

        <TextView
            android:id="@+id/text_unread_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/bg_unread_count"
            android:paddingStart="6dp"
            android:paddingTop="2dp"
            android:paddingEnd="6dp"
            android:paddingBottom="2dp"
            android:text="5"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/text_admin_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:text="Admin Name" />

            <TextView
                android:id="@+id/text_admin_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#7986CB"
                android:text="Admin Description"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="16sp"
                android:textStyle="bold"
                android:textColor="#3F51B5"
                android:text="Admin Name" />

            <TextView
                android:id="@+id/text_roll_number"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#5C6BC0"
                android:text="Roll Number"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_email"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#5C6BC0"
                android:text="Email Address"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_mobile"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#5C6BC0"
                android:text="Mobile Number"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_committee"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#5C6BC0"
                android:text="Committee"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_designation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#5C6BC0"
                android:text="Designation"
                android:layout_marginTop="4dp" />

            <TextView
                android:id="@+id/text_details"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textSize="14sp"
                android:textColor="#7986CB"
                android:text="Additional details..."
                android:layout_marginTop="8dp" />
        </LinearLayout>
    </RelativeLayout>

</androidx.cardview.widget.CardView> 