<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:paddingHorizontal="8dp">

    <FrameLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <!-- Golden border for Admin1 users -->
        <View
            android:id="@+id/view_admin_border"
            android:layout_width="76dp"
            android:layout_height="76dp"
            android:background="@drawable/bg_golden_border"
            android:visibility="gone"
            tools:visibility="visible" />

        <androidx.cardview.widget.CardView
            android:layout_width="70dp"
            android:layout_height="70dp"
            android:layout_gravity="center"
            app:cardCornerRadius="35dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/image_user_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/ic_launcher_foreground"
                android:background="@color/ui_blue"
                android:contentDescription="User avatar" />

        </androidx.cardview.widget.CardView>

        <View
            android:id="@+id/view_important_indicator"
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:layout_gravity="end|bottom"
            android:background="@drawable/badge_important"
            android:visibility="gone"
            tools:visibility="visible" />

    </FrameLayout>

    <TextView
        android:id="@+id/text_user_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:maxWidth="80dp"
        android:textAlignment="center"
        android:textColor="@color/ui_white"
        android:textSize="12sp"
        tools:text="Aditya" />

</LinearLayout> 