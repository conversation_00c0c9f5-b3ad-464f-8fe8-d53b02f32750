package com.phad.chatapp.adapters

import android.content.Context
import android.content.Intent
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.phad.chatapp.ChatActivity
import com.phad.chatapp.R
import com.phad.chatapp.models.AdminForChat
import com.phad.chatapp.utils.SessionManager

/**
 * Adapter for displaying search results
 */
class UserSearchAdapter(
    private val context: Context,
    private var users: List<AdminForChat> = emptyList(),
    private val sessionManager: SessionManager = SessionManager(context),
    private val onItemClick: ((AdminForChat) -> Unit)? = null
) : RecyclerView.Adapter<UserSearchAdapter.SearchResultViewHolder>() {

    fun updateData(newUsers: List<AdminForChat>) {
        this.users = newUsers
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchResultViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_search_result, parent, false)
        return SearchResultViewHolder(view)
    }

    override fun onBindViewHolder(holder: SearchResultViewHolder, position: Int) {
        val user = users[position]
        holder.bind(user)
    }

    override fun getItemCount(): Int = users.size

    inner class SearchResultViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        private val imageAvatar: ImageView = itemView.findViewById(R.id.image_user_avatar)
        private val textName: TextView = itemView.findViewById(R.id.text_user_name)
        private val textUserType: TextView = itemView.findViewById(R.id.text_user_type)
        private val adminBorder: View = itemView.findViewById(R.id.view_admin_border)

        fun bind(user: AdminForChat) {
            textName.text = user.name
            textUserType.text = user.userType
            
            // Show golden border for Admin1 users
            adminBorder.visibility = if (user.userType == "Admin1") View.VISIBLE else View.GONE

            // Set click listener to open chat with this user
            itemView.setOnClickListener {
                if (onItemClick != null) {
                    // Use the callback if provided
                    onItemClick.invoke(user)
                } else {
                    // Default behavior - start chat activity
                    startChat(user)
                }
            }
        }
        
        private fun startChat(user: AdminForChat) {
            // Get current user ID from session
            val currentUserId = sessionManager.fetchUserId()
            if (currentUserId.isEmpty()) {
                return
            }

            // Start chat activity
            val intent = Intent(context, ChatActivity::class.java).apply {
                putExtra("currentUserRollNumber", currentUserId)
                putExtra("currentUserName", sessionManager.fetchUserName())
                putExtra("otherUserRollNumber", user.rollNumber)
                putExtra("otherUserName", user.name)
            }
            context.startActivity(intent)
        }
    }
} 