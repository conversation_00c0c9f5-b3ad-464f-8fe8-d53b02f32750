<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Shadow layer -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#20000000" />
            <corners android:radius="34dp" />
        </shape>
    </item>
    
    <!-- Background with top offset to create shadow effect -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/ui_dark" />
            <corners android:radius="33dp" />
            <stroke
                android:width="0.5dp"
                android:color="#55FFFFFF" />
        </shape>
    </item>
    
    <!-- Inner padding -->
    <item android:top="1dp" android:left="1dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <padding
                android:bottom="8dp"
                android:left="16dp"
                android:right="16dp"
                android:top="8dp" />
            <corners android:radius="32dp" />
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</layer-list> 