package com.phad.chatapp

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.text.TextUtils
import android.text.method.HideReturnsTransformationMethod
import android.text.method.PasswordTransformationMethod
import android.util.Log
import android.view.View
import android.widget.Toast
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseAuthInvalidCredentialsException
import com.google.firebase.auth.FirebaseAuthInvalidUserException
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.databinding.ActivityLoginFormBinding
import com.phad.chatapp.utils.SessionManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import android.app.AlertDialog

class LoginFormActivity : AppCompatActivity() {
    private lateinit var binding: ActivityLoginFormBinding
    private lateinit var auth: FirebaseAuth
    private lateinit var db: FirebaseFirestore
    private lateinit var sessionManager: SessionManager
    private val TAG = "LoginFormActivity"
    
    // To track verification state
    private var userDocument: Map<String, Any>? = null
    private var isPasswordVisible = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginFormBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Initialize Firebase Auth and Firestore
        auth = FirebaseAuth.getInstance()
        db = FirebaseFirestore.getInstance()
        sessionManager = SessionManager(this)

        setupUI()
    }

    private fun setupUI() {
        // Back button click listener
        binding.btnBack.setOnClickListener {
            finish()
        }
        
        // Password visibility toggle
        binding.passwordVisibilityToggle.setOnClickListener {
            isPasswordVisible = !isPasswordVisible
            if (isPasswordVisible) {
                // Show password
                binding.etPassword.transformationMethod = HideReturnsTransformationMethod.getInstance()
                binding.passwordVisibilityToggle.alpha = 1.0f
            } else {
                // Hide password
                binding.etPassword.transformationMethod = PasswordTransformationMethod.getInstance()
                binding.passwordVisibilityToggle.alpha = 0.6f
            }
            // Move cursor to end
            binding.etPassword.setSelection(binding.etPassword.text.length)
        }

        // Email check mark initially hidden
        binding.emailCheckMark.visibility = View.GONE
        
        // Login button click listener
        binding.btnLogin.setOnClickListener {
            if (validateInputs()) {
                performLogin()
            }
        }
        
        // Remove roll number focus change listener that auto-filled email
        // This improves security by requiring manual email entry
        
        // Forgot password listener
        binding.textViewForgotPassword.setOnClickListener {
            handleForgotPassword()
        }
    }

    private fun validateInputs(): Boolean {
        val rollNumber = binding.etRollNumber.text.toString().trim()
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()

        if (TextUtils.isEmpty(rollNumber)) {
            showToast("Roll number is required")
            return false
        }

        if (TextUtils.isEmpty(email)) {
            showToast("Email is required")
            return false
        }

        if (TextUtils.isEmpty(password)) {
            showToast("Password is required")
            return false
        }

        return true
    }
    
    private fun checkUserWithRollNumber(rollNumber: String) {
        // Show loading
        binding.progressBar.visibility = View.VISIBLE
        
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Check if user exists in Firestore with the provided roll number
                val documentSnapshot = db.collection("users")
                    .document(rollNumber)
                    .get()
                    .await()
                
                withContext(Dispatchers.Main) {
                    if (documentSnapshot.exists()) {
                        // User exists, store the user data for later verification
                        userDocument = documentSnapshot.data
                        
                        // Set email if available
                        val firestoreEmail = userDocument?.get("email") as? String
                        if (!firestoreEmail.isNullOrEmpty()) {
                            binding.etEmail.setText(firestoreEmail)
                            binding.emailCheckMark.visibility = View.VISIBLE
                        } else {
                            binding.emailCheckMark.visibility = View.GONE
                        }
                        
                        binding.progressBar.visibility = View.GONE
                    } else {
                        // User not found
                        binding.progressBar.visibility = View.GONE
                        showToast("User not found with this roll number")
                        binding.emailCheckMark.visibility = View.GONE
                        userDocument = null
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    Log.e(TAG, "Error checking roll number", e)
                    showToast("Error: ${e.message}")
                    binding.emailCheckMark.visibility = View.GONE
                    userDocument = null
                }
            }
        }
    }

    private fun performLogin() {
        val rollNumber = binding.etRollNumber.text.toString().trim()
        val email = binding.etEmail.text.toString().trim()
        val password = binding.etPassword.text.toString().trim()

        binding.progressBar.visibility = View.VISIBLE
        
        // First check if roll number exists and email matches
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // If userDocument is null, we need to fetch it
                if (userDocument == null) {
                    val documentSnapshot = db.collection("users")
                        .document(rollNumber)
                        .get()
                        .await()
                    
                    if (!documentSnapshot.exists()) {
                        withContext(Dispatchers.Main) {
                            binding.progressBar.visibility = View.GONE
                            showToast("User not found with this roll number")
                        }
                        return@launch
                    }
                    
                    userDocument = documentSnapshot.data
                }
                
                // Verify email matches what's in Firestore
                val firestoreEmail = userDocument?.get("email") as? String
                if (firestoreEmail.isNullOrEmpty()) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        showToast("Email not found in user data. Please contact administrator.")
                    }
                    return@launch
                }
                
                // Case-insensitive email comparison
                if (firestoreEmail.trim().lowercase() != email.trim().lowercase()) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        showToast("The provided email does not match the email registered with this roll number.")
                    }
                    return@launch
                }
                
                // Now attempt to authenticate with Firebase Auth
                try {
                    auth.signInWithEmailAndPassword(email, password).await()
                    
                    // Login successful - extract user data
                    val userType = userDocument?.get("userType") as? String ?: "Student"
                    val year = (userDocument?.get("year") as? Long)?.toInt() ?: 1
                    val fullName = userDocument?.get("name") as? String ?: ""
                    
                    // Create login session
                    sessionManager.createLoginSession(userType, rollNumber, year)
                    sessionManager.saveUserName(fullName)
                    
                    // Update FCM token
                    val currentUser = auth.currentUser
                    if (currentUser != null) {
                        try {
                            val token = com.google.firebase.messaging.FirebaseMessaging.getInstance().token.await()
                            db.collection("users")
                                .document(rollNumber)
                                .update("fcmToken", token)
                                .await()
                        } catch (e: Exception) {
                            Log.e(TAG, "Error updating FCM token", e)
                            // Continue anyway
                        }
                    }
                    
                    // Navigate to MainActivity
                    withContext(Dispatchers.Main) {
                        val intent = Intent(this@LoginFormActivity, MainActivity::class.java)
                        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
                        startActivity(intent)
                        finish()
                    }
                } catch (e: Exception) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        when (e) {
                            is FirebaseAuthInvalidUserException -> 
                                showToast("Authentication failed: Email not registered")
                            is FirebaseAuthInvalidCredentialsException -> 
                                showToast("Authentication failed: Invalid password")
                            else -> 
                                showToast("Authentication failed: ${e.message}")
                        }
                        Log.e(TAG, "Authentication error", e)
                    }
                }
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    showToast("Error: ${e.message}")
                    Log.e(TAG, "Login error", e)
                }
            }
        }
    }
    
    private fun sendPasswordResetEmail(email: String) {
        binding.progressBar.visibility = View.VISIBLE
        
        auth.sendPasswordResetEmail(email)
            .addOnCompleteListener { task ->
                binding.progressBar.visibility = View.GONE
                if (task.isSuccessful) {
                    showToast("Password reset email sent to $email")
                } else {
                    showToast("Failed to send password reset email: ${task.exception?.message}")
                    Log.e(TAG, "Password reset failed", task.exception)
                }
            }
    }
    
    private fun showToast(message: String) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show()
    }

    // Add new method for handling forgot password with confirmation
    private fun handleForgotPassword() {
        val rollNumber = binding.etRollNumber.text.toString().trim()
        val email = binding.etEmail.text.toString().trim()
        
        if (email.isEmpty()) {
            showToast("Please enter your email address first")
            return
        }
        
        if (rollNumber.isEmpty()) {
            showToast("Please enter your roll number first")
            return
        }
        
        // Show loading state
        binding.progressBar.visibility = View.VISIBLE
        
        // Verify email exists in database and matches the roll number
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // Check if the roll number exists
                val userDoc = db.collection("users")
                    .document(rollNumber)
                    .get()
                    .await()
                
                if (!userDoc.exists()) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        showToast("User not found with this roll number")
                    }
                    return@launch
                }
                
                // Verify email matches what's in Firestore
                val firestoreEmail = userDoc.getString("email")
                if (firestoreEmail.isNullOrEmpty()) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        showToast("Email not found in user data. Please contact administrator.")
                    }
                    return@launch
                }
                
                // Case-insensitive email comparison
                if (firestoreEmail.trim().lowercase() != email.trim().lowercase()) {
                    withContext(Dispatchers.Main) {
                        binding.progressBar.visibility = View.GONE
                        showToast("The provided email does not match the email registered with this roll number.")
                    }
                    return@launch
                }
                
                // Email is verified, show confirmation dialog
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    showPasswordResetConfirmation(email)
                }
                
            } catch (e: Exception) {
                withContext(Dispatchers.Main) {
                    binding.progressBar.visibility = View.GONE
                    showToast("Error checking user data: ${e.message}")
                    Log.e(TAG, "Error in forgot password", e)
                }
            }
        }
    }
    
    private fun showPasswordResetConfirmation(email: String) {
        AlertDialog.Builder(this)
            .setTitle("Reset Password")
            .setMessage("Are you sure you want to reset your password? A password reset link will be sent to $email")
            .setPositiveButton("Yes") { _, _ ->
                sendPasswordResetEmail(email)
            }
            .setNegativeButton("No", null)
            .show()
    }
} 