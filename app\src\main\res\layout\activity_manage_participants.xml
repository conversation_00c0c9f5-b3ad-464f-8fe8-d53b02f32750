<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout 
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="#0D0302">

    <!-- Status Bar Spacer - Will be set programmatically (reduced height) -->
    <View
        android:id="@+id/status_bar_spacer"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#0D0302"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Toolbar - Moved up directly below status bar -->
    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="?attr/actionBarSize"
        android:background="#0D0302"
        android:elevation="4dp"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- Back button changed to larger backward arrow -->
            <ImageView
                android:id="@+id/back_button"
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:layout_marginStart="4dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="Back"
                android:padding="4dp"
                android:src="@android:drawable/ic_media_previous"
                app:tint="#FFFFFF"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Title text - positioned directly after back button -->
            <TextView
                android:id="@+id/toolbar_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:text="Manage Participants"
                android:textColor="#FFFFFF"
                android:textSize="20sp"
                android:textStyle="bold"
                android:ellipsize="end"
                android:maxLines="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/back_button"
                app:layout_constraintEnd_toStartOf="@id/search_button"
                app:layout_constraintTop_toTopOf="parent" />

            <!-- Search button (unchanged position) -->
            <ImageView
                android:id="@+id/search_button"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="16dp"
                android:src="@android:drawable/ic_menu_search"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="#FFFFFF" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.appcompat.widget.Toolbar>

    <!-- Search bar (initially hidden) -->
    <androidx.cardview.widget.CardView
        android:id="@+id/search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="8dp"
        android:visibility="gone"
        app:cardBackgroundColor="?attr/colorSurface"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp"
        app:layout_constraintTop_toBottomOf="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:padding="8dp">

            <EditText
                android:id="@+id/search_edit_text"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:background="@null"
                android:hint="Search participants..."
                android:imeOptions="actionSearch"
                android:inputType="text"
                android:padding="8dp"
                android:textSize="16sp"
                android:textColor="?attr/colorOnSurface"
                android:textColorHint="?attr/colorOnSurface"
                android:alpha="0.6" />

            <ImageView
                android:id="@+id/clear_search_button"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_gravity="center_vertical"
                android:src="@android:drawable/ic_menu_close_clear_cancel"
                android:padding="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true"
                app:tint="?attr/colorOnSurface" />
        </LinearLayout>
    </androidx.cardview.widget.CardView>

    <!-- Content container with white background -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/search_container">

        <!-- Info text at the top -->
        <TextView
            android:id="@+id/info_text"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="#FFCC00"
            android:padding="12dp"
            android:text="Select users to add to the group. Users with checkmarks will be included in the group."
            android:textColor="#0D0302"
            android:textSize="14sp"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- Divider -->
        <View
            android:id="@+id/divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginTop="8dp"
            android:background="#E0E0E0"
            app:layout_constraintTop_toBottomOf="@id/info_text" />

        <!-- Label for participants -->
            <TextView
                android:id="@+id/all_users_label"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginTop="16dp"
            android:text="All Users (0 selected)"
            android:textColor="#0D0302"
                android:textSize="14sp"
            android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/divider" />

        <!-- RecyclerView for participants list -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/all_users_recycler"
                android:layout_width="match_parent"
            android:layout_height="0dp"
                android:layout_marginTop="8dp"
            android:layout_marginBottom="8dp"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintBottom_toTopOf="@id/save_button"
                app:layout_constraintTop_toBottomOf="@id/all_users_label"
                tools:listitem="@layout/item_participant" />

        <!-- Save Changes Button -->
        <Button
            android:id="@+id/save_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_margin="16dp"
            android:background="@drawable/rounded_button"
            android:backgroundTint="#006BFF"
            android:padding="12dp"
            android:text="Save Changes"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Progress bar -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout> 