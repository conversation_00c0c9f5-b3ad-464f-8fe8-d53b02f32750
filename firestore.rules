rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }
    
    // Check if the user is an admin
    function isAdmin() {
      return isAuthenticated() && 
        exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    // Check if the current user is the owner of the document
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    // Teaching events collection - Ad<PERSON> can create/edit/delete, all authenticated users can read
    match /teaching_events/{eventId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin();
    }
    
    // General events collection - Ad<PERSON> can create/edit/delete, all authenticated users can read
    match /general_events/{eventId} {
      allow read: if isAuthenticated();
      allow create, update, delete: if isAdmin();
    }
    
    // Leave applications - Users can create their own applications, admin can read/update all
    match /leave_applications/{applicationId} {
      allow read: if isAdmin() || isOwner(resource.data.userId);
      allow create: if isAuthenticated() && request.resource.data.userId == request.auth.uid;
      allow update, delete: if isAdmin();
    }
    
    // Accepted leaves - Admin can create/update, users can read their own accepted leaves
    match /accepted_leaves/{leaveId} {
      allow read: if isAdmin() || isOwner(resource.data.userId);
      allow create, update, delete: if isAdmin();
    }
  }
} 