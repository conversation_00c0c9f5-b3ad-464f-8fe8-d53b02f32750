package com.phad.chatapp.models

import java.io.Serializable
import java.util.Date
import java.util.concurrent.TimeUnit

data class Update(
    val id: String = "",
    val authorId: String = "",
    val authorName: String = "",
    val authorImageUrl: String? = null,
    val title: String? = null,
    val content: String = "",
    val mediaUrl: String? = null,
    val imageUrl: String? = null,
    val documentUrl: String? = null,
    val documentName: String? = null,
    val qrCodeUrl: String? = null,
    val url: String? = null,
    val externalLink: String? = null,
    val timestamp: Long = 0,
    val targetGroups: List<String> = listOf(),
    val category: String = "general",
    val locationName: String? = null,
    val locationAddress: String? = null,
    val locationLatitude: Double? = null,
    val locationLongitude: Double? = null,
    val eventDate: Long? = null,
    val eventEndDate: Long? = null,
    val isImportant: Boolean = false,
    val tags: List<String> = listOf()
) : Serializable {
    // No-argument constructor for Firestore
    constructor() : this(
        id = "",
        authorId = "",
        authorName = "",
        authorImageUrl = null,
        title = null,
        content = "",
        mediaUrl = null,
        imageUrl = null,
        documentUrl = null,
        documentName = null,
        qrCodeUrl = null,
        url = null,
        externalLink = null,
        timestamp = 0,
        targetGroups = listOf(),
        category = "general",
        locationName = null,
        locationAddress = null,
        locationLatitude = null,
        locationLongitude = null,
        eventDate = null,
        eventEndDate = null,
        isImportant = false,
        tags = listOf()
    )
    
    fun getTimeAgo(): String {
        val now = Date().time
        val diff = now - timestamp
        
        return when {
            diff < TimeUnit.MINUTES.toMillis(1) -> "Just now"
            diff < TimeUnit.HOURS.toMillis(1) -> "${TimeUnit.MILLISECONDS.toMinutes(diff)} min ago"
            diff < TimeUnit.DAYS.toMillis(1) -> "${TimeUnit.MILLISECONDS.toHours(diff)} hr ago"
            diff < TimeUnit.DAYS.toMillis(7) -> "${TimeUnit.MILLISECONDS.toDays(diff)} days ago"
            else -> {
                val date = Date(timestamp)
                val formatter = java.text.SimpleDateFormat("MMM dd, yyyy", java.util.Locale.getDefault())
                formatter.format(date)
            }
        }
    }
    
    fun getFormattedEventDate(): String? {
        if (eventDate == null) return null
        
        val date = Date(eventDate)
        val formatter = java.text.SimpleDateFormat("EEE, MMM dd, yyyy", java.util.Locale.getDefault())
        return formatter.format(date)
    }
    
    fun hasLocation(): Boolean {
        return locationName != null || locationLatitude != null && locationLongitude != null
    }
    
    fun getCategoryDisplay(): String {
        return when (category.lowercase()) {
            "academic" -> "Academic"
            "event" -> "Event"
            "announcement" -> "Announcement"
            "notice" -> "Notice"
            else -> "General"
        }
    }
} 