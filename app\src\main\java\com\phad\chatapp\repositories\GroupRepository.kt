package com.phad.chatapp.repositories

import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.firebase.Timestamp
import com.google.firebase.firestore.FirebaseFirestore
import com.google.firebase.firestore.QuerySnapshot
import com.phad.chatapp.models.Group
import java.util.Date

class GroupRepository {
    private val TAG = "GroupRepository"
    private val db = FirebaseFirestore.getInstance()
    private val groupsCollection = db.collection("groups")
    
    fun getCommunityGroups(): Task<QuerySnapshot> {
        Log.d(TAG, "Getting community groups")
        return groupsCollection.get()
    }
    
    fun createGroup(group: Group): Task<Void> {
        Log.d(TAG, "Creating new group: ${group.name}")
        
        // Create a document ID based on the group name
        // Replace spaces with underscores and remove special characters
        val baseDocId = group.name.trim()
            .replace("\\s+".toRegex(), "_")
            .replace("[^a-zA-Z0-9_]".toRegex(), "")
            .take(30) // Limit length to avoid very long IDs
        
        // Add a timestamp suffix to ensure uniqueness
        val timestamp = System.currentTimeMillis()
        val docId = "${baseDocId}_$timestamp"
        
        val newGroupRef = groupsCollection.document(docId)
        val groupWithId = group.copy(id = docId)
        
        // Set the timestamp for creation time if not already set
        if (groupWithId.createdAt == null) {
            groupWithId.createdAt = Timestamp.now()
        }
        
        Log.d(TAG, "Setting document ID to: $docId")
        return newGroupRef.set(groupWithId)
    }
    
    fun deleteGroup(groupId: String): Task<Void> {
        Log.d(TAG, "Deleting group: $groupId")
        return groupsCollection.document(groupId).delete()
    }
    
    fun getGroupById(groupId: String): Task<com.google.firebase.firestore.DocumentSnapshot> {
        Log.d(TAG, "Getting group by ID: $groupId")
        return groupsCollection.document(groupId).get()
    }
    
    fun getGroupsByParticipant(userId: String): Task<QuerySnapshot> {
        Log.d(TAG, "Getting groups for participant: $userId")
        return groupsCollection.whereArrayContains("participants", userId).get()
    }
} 