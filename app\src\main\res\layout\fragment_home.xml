<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/white"
    tools:context=".fragments.HomeFragment">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="16dp">

            <!-- Header with Action Icons -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/headerLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="16dp"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingBottom="8dp">

                <!-- Left side empty space for logo -->
                <View
                    android:id="@+id/logoPlaceholder"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageButton
                    android:id="@+id/todoButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_todo"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Task List"
                    android:tint="@android:color/black"
                    app:layout_constraintEnd_toStartOf="@+id/chatbotButton"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginEnd="16dp" />

                <ImageButton
                    android:id="@+id/chatbotButton"
                    android:layout_width="40dp"
                    android:layout_height="40dp"
                    android:src="@drawable/ic_chatbot"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:contentDescription="Chatbot"
                    android:tint="@android:color/black"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Greeting Section with curved background -->
            <androidx.cardview.widget.CardView
                android:id="@+id/greetingCard"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:layout_marginEnd="16dp"
                android:layout_marginTop="16dp"
                app:cardCornerRadius="12dp"
                app:cardElevation="4dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:id="@+id/greetingTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="24sp"
                        android:textStyle="bold"
                        android:textColor="@android:color/black"
                        android:text="Good Morning" />

                    <TextView
                        android:id="@+id/userNameTextView"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textSize="18sp"
                        android:textStyle="normal"
                        android:textColor="@android:color/black"
                        tools:text="Aditya G..." />

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="#E0E0E0"
                        android:layout_marginTop="12dp"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">
                        
                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:text="Your Next Class is on" />
                            
                        <TextView
                            android:id="@+id/nextClassDateTimeTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="16sp"
                            android:textColor="@android:color/black"
                            android:text="24 April, 3:00 PM" />
                            
                        <TextView
                            android:id="@+id/nextClassLocationTextView"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textSize="14sp"
                            android:textColor="#757575"
                            android:text="in Raghunpur" />
                    </LinearLayout>
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <!-- Updates Section -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Updates"
                android:textSize="20sp"
                android:textStyle="bold"
                android:textColor="@android:color/black"
                android:paddingStart="16dp"
                android:paddingEnd="16dp"
                android:paddingTop="24dp"
                android:paddingBottom="8dp" />

            <!-- QR Code Update Cards -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/updatesRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:orientation="horizontal"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                tools:listitem="@layout/item_update_card"
                tools:itemCount="2" />

            <ProgressBar
                android:id="@+id/updatesProgressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:visibility="gone" />

            <TextView
                android:id="@+id/noUpdatesTextView"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:text="No updates available"
                android:textSize="16sp"
                android:textColor="#757575"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Admin FAB for creating updates -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/addUpdateFab"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:src="@drawable/ic_add"
        android:contentDescription="Add Update"
        app:fabSize="normal"
        app:tint="@android:color/white"
        android:visibility="gone" />

    <!-- This bottom navigation is redundant and will be hidden in code -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottomNavigation"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom"
        android:background="@android:color/white"
        app:menu="@menu/bottom_navigation_menu"
        app:itemIconTint="@color/bottom_nav_colors"
        app:itemTextColor="@color/bottom_nav_colors" />

</androidx.coordinatorlayout.widget.CoordinatorLayout> 