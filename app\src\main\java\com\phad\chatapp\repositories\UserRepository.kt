package com.phad.chatapp.repositories

import android.util.Log
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskCompletionSource
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.models.User
import kotlin.Exception

class UserRepository {
    private val TAG = "UserRepository"
    private val db = FirebaseFirestore.getInstance()
    
    fun getAllUsers(): Task<List<User>> {
        val taskCompletionSource = TaskCompletionSource<List<User>>()
        val allUsers = mutableListOf<User>()
        
        // Get Admin1 users first
        db.collection("Admin1").get()
            .addOnSuccessListener { admin1Result ->
                // Process Admin1 users
                for (doc in admin1Result.documents) {
                    try {
                        Log.d(TAG, "Processing Admin1 user: ${doc.id}")
                        allUsers.add(
                            User(
                                id = doc.id,
                                name = doc.getString("name") ?: "",
                                email = doc.getString("email") ?: "",
                                rollNumber = doc.id,
                                userType = "Admin1",
                                description = "",
                                contactNumber = ""
                            ).apply {
                                setYear(0)
                            }
                        )
                    } catch (e: Exception) {
                        Log.e(TAG, "Error processing Admin1 user: ${doc.id}", e)
                    }
                }
                
                // Get Admin2 users next
                db.collection("Admin2").get()
                    .addOnSuccessListener { admin2Result ->
                        // Process Admin2 users
                        for (doc in admin2Result.documents) {
                            try {
                                Log.d(TAG, "Processing Admin2 user: ${doc.id}")
                                allUsers.add(
                                    User(
                                        id = doc.id,
                                        name = doc.getString("name") ?: "",
                                        email = doc.getString("email") ?: "",
                                        rollNumber = doc.id,
                                        userType = "Admin2",
                                        description = "",
                                        contactNumber = ""
                                    ).apply {
                                        setYear(0)
                                    }
                                )
                            } catch (e: Exception) {
                                Log.e(TAG, "Error processing Admin2 user: ${doc.id}", e)
                            }
                        }
                        
                        // Finally get Students
                        db.collection("Students").get()
                            .addOnSuccessListener { studentsResult ->
                                // Process Student users
                                for (doc in studentsResult.documents) {
                                    try {
                                        Log.d(TAG, "Processing Student user: ${doc.id}")
                                        val year = try {
                                            when (val yearValue = doc.get("year")) {
                                                is Long -> yearValue.toInt()
                                                is Int -> yearValue
                                                is Double -> yearValue.toInt()
                                                is String -> yearValue.toIntOrNull() ?: 0
                                                else -> 0
                                            }
                                        } catch (e: Exception) {
                                            Log.w(TAG, "Failed to parse year for user ${doc.id}", e)
                                            0
                                        }
                                        
                                        allUsers.add(
                                            User(
                                                id = doc.id,
                                                name = doc.getString("name") ?: "",
                                                email = doc.getString("email") ?: "",
                                                rollNumber = doc.id,
                                                userType = "Student",
                                                description = "",
                                                contactNumber = ""
                                            ).apply {
                                                setYear(year)
                                            }
                                        )
                                    } catch (e: Exception) {
                                        Log.e(TAG, "Error processing Student user: ${doc.id}", e)
                                    }
                                }
                                
                                // Complete the task with all users
                                Log.d(TAG, "All users loaded: ${allUsers.size}")
                                taskCompletionSource.setResult(allUsers)
                            }
                            .addOnFailureListener { exception ->
                                Log.e(TAG, "Failed to get Students", exception)
                                taskCompletionSource.setException(exception)
                            }
                    }
                    .addOnFailureListener { exception ->
                        Log.e(TAG, "Failed to get Admin2 users", exception)
                        taskCompletionSource.setException(exception)
                    }
            }
            .addOnFailureListener { exception ->
                Log.e(TAG, "Failed to get Admin1 users", exception)
                taskCompletionSource.setException(exception)
            }
        
        return taskCompletionSource.task
    }
    
    fun getUserById(userId: String): Task<User> {
        val taskCompletionSource = TaskCompletionSource<User>()
        Log.d(TAG, "Looking up user with ID: $userId")
        
        // First try Admin1 collection
        db.collection("Admin1").document(userId).get()
            .addOnSuccessListener { documentSnapshot ->
                if (documentSnapshot.exists()) {
                    // Found in Admin1
                    try {
                        Log.d(TAG, "Found user $userId in Admin1")
                        val user = createUserFromDocument(documentSnapshot, "Admin1")
                        taskCompletionSource.setResult(user)
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing Admin1 user $userId", e)
                        taskCompletionSource.setException(e)
                    }
                } else {
                    // Try Admin2 collection
                    db.collection("Admin2").document(userId).get()
                        .addOnSuccessListener { admin2Doc ->
                            if (admin2Doc.exists()) {
                                // Found in Admin2
                                try {
                                    Log.d(TAG, "Found user $userId in Admin2")
                                    val user = createUserFromDocument(admin2Doc, "Admin2")
                                    taskCompletionSource.setResult(user)
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error parsing Admin2 user $userId", e)
                                    taskCompletionSource.setException(e)
                                }
                            } else {
                                // Try Students collection
                                db.collection("Students").document(userId).get()
                                    .addOnSuccessListener { studentDoc ->
                                        if (studentDoc.exists()) {
                                            // Found in Students
                                            try {
                                                Log.d(TAG, "Found user $userId in Students")
                                                val user = createUserFromDocument(studentDoc, "Student")
                                                taskCompletionSource.setResult(user)
                                            } catch (e: Exception) {
                                                Log.e(TAG, "Error parsing Student user $userId", e)
                                                taskCompletionSource.setException(e)
                                            }
                                        } else {
                                            // Try in the 'users' collection
                                            db.collection("users").document(userId).get()
                                                .addOnSuccessListener { userDoc ->
                                                    if (userDoc.exists()) {
                                                        Log.d(TAG, "Found user $userId in 'users' collection")
                                                        try {
                                                            val userType = userDoc.getString("userType") ?: "Student"
                                                            val user = createUserFromDocument(userDoc, userType)
                                                            taskCompletionSource.setResult(user)
                                                        } catch (e: Exception) {
                                                            Log.e(TAG, "Error parsing user $userId from 'users' collection", e)
                                                            taskCompletionSource.setException(e)
                                                        }
                                                    } else {
                                                        // Not found anywhere
                                                        Log.w(TAG, "User $userId not found in any collection")
                                                        // Return a default User instead of null
                                                        taskCompletionSource.setResult(User(id = userId, rollNumber = userId))
                                                    }
                                                }
                                                .addOnFailureListener { e ->
                                                    Log.e(TAG, "Error querying users collection for $userId", e)
                                                    taskCompletionSource.setException(e)
                                                }
                                        }
                                    }
                                    .addOnFailureListener { exception ->
                                        Log.e(TAG, "Error querying Students collection for $userId", exception)
                                        taskCompletionSource.setException(exception)
                                    }
                            }
                        }
                        .addOnFailureListener { exception ->
                            Log.e(TAG, "Error querying Admin2 collection for $userId", exception)
                            taskCompletionSource.setException(exception)
                        }
                }
            }
            .addOnFailureListener { exception ->
                Log.e(TAG, "Error querying Admin1 collection for $userId", exception)
                taskCompletionSource.setException(exception)
            }
        
        return taskCompletionSource.task
    }

    private fun createUserFromDocument(documentSnapshot: com.google.firebase.firestore.DocumentSnapshot, userType: String): User {
        try {
            // Extract user data safely
            val data = documentSnapshot.data ?: mapOf<String, Any>()
            
            return User(
                id = documentSnapshot.id,
                name = data["name"] as? String ?: "",
                email = data["email"] as? String ?: "",
                rollNumber = documentSnapshot.id,
                userType = userType,
                description = data["description"] as? String ?: "",
                contactNumber = data["contact_number"] as? String ?: "",
                profileImageUrl = data["profile_image_url"] as? String
            ).apply {
                // Safely set year value from any type
                setYear(data["year"])
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error creating user from document: ${e.message}", e)
            // Return a default User as fallback
            return User(
                id = documentSnapshot.id,
                rollNumber = documentSnapshot.id,
                userType = userType
            )
        }
    }
} 