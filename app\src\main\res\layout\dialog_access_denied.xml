<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    app:cardCornerRadius="16dp"
    app:cardBackgroundColor="#1F1F1F">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <TextView
            android:id="@+id/dialog_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Access Denied"
            android:textColor="#FFFFFF"
            android:textSize="18sp"
            android:textStyle="bold"
            android:layout_marginBottom="16dp" />

        <TextView
            android:id="@+id/dialog_message"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="To enter the group, contact the admin with your roll number and name."
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:layout_marginBottom="24dp"
            android:lineSpacingExtra="4dp" />

        <Button
            android:id="@+id/close_button"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="OK"
            android:backgroundTint="#313131"
            android:textColor="#FFFFFF" />
    </LinearLayout>
</androidx.cardview.widget.CardView> 