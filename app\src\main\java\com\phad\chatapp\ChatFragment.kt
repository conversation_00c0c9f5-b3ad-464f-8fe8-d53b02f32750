package com.phad.chatapp

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.view.inputmethod.EditorInfo
import android.view.inputmethod.InputMethodManager
import android.widget.EditText
import android.widget.ImageButton
import android.widget.PopupWindow
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.appcompat.widget.PopupMenu
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.firebase.firestore.FirebaseFirestore
import com.phad.chatapp.adapters.CommunitiesAdapter
import com.phad.chatapp.adapters.UnreadMessageAdapter
import com.phad.chatapp.adapters.UserAvatarAdapter
import com.phad.chatapp.adapters.UserSearchAdapter
import com.phad.chatapp.models.AdminForChat
import com.phad.chatapp.models.Group
import com.phad.chatapp.models.UnreadMessage
import com.phad.chatapp.repositories.UnreadMessageRepository
import com.phad.chatapp.utils.SessionManager
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

class ChatFragment : Fragment() {
    private val TAG = "ChatFragment"
    
    private lateinit var recyclerUserAvatars: RecyclerView
    private lateinit var recyclerCommunities: RecyclerView
    private lateinit var sessionManager: SessionManager
    
    private var userType: String = ""
    private var userRollNumber: String = ""

    override fun onCreateView(
        inflater: LayoutInflater, 
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.fragment_chat, container, false)
        
        Log.d(TAG, "ChatFragment onCreateView called")
        
        // Initialize session manager
        sessionManager = SessionManager(requireContext())
        
        // Get user info from session
        userType = sessionManager.fetchUserType() ?: ""
        userRollNumber = sessionManager.fetchUserId()
        
        // Initialize UI components
        initializeUI(view)
        
        return view
    }
    
    private fun initializeUI(view: View) {
        // Initialize RecyclerViews
        recyclerUserAvatars = view.findViewById(R.id.recycler_user_avatars)
        recyclerCommunities = view.findViewById(R.id.recycler_communities)
        
        // Set up horizontal layout for user avatars
        recyclerUserAvatars.layoutManager = LinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        
        // Set up vertical layout for communities
        recyclerCommunities.layoutManager = LinearLayoutManager(context)
        
        // Set up search and settings buttons
        val btnSearch = view.findViewById<ImageButton>(R.id.btn_search)
        val btnSettings = view.findViewById<ImageButton>(R.id.btn_settings)
        
        btnSearch.setOnClickListener {
            showSearchDialog()
        }
        
        btnSettings.setOnClickListener { view ->
            showSettingsMenu(view)
        }
        
        // Load data
        loadUserAvatars()
        loadCommunities()
    }
    
    private fun showSettingsMenu(view: View) {
        val popupMenu = PopupMenu(requireContext(), view)
        
        // Inflate the menu based on user type
        if (userType == "Admin1") {
            // Admin1 gets full menu with create group, remove group options
            popupMenu.menuInflater.inflate(R.menu.settings_admin_menu, popupMenu.menu)
        } else {
            // Non-admin users only get basic options
            popupMenu.menuInflater.inflate(R.menu.settings_user_menu, popupMenu.menu)
        }
        
        // Set up click listener for menu items
        popupMenu.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_refresh -> {
                    refreshData()
                    true
                }
                R.id.action_create_group -> {
                    showCreateGroupScreen()
                    true
                }
                R.id.action_remove_group -> {
                    showRemoveGroupScreen()
                    true
                }
                R.id.action_unread_messages -> {
                    showUnreadMessagesPopup()
                    true
                }
                else -> false
            }
        }
        
        // Show the popup menu
        popupMenu.show()
    }
    
    private fun showCreateGroupScreen() {
        // Navigate to AddGroupFragment
        val fragment = AddGroupFragment()
        requireActivity().supportFragmentManager.beginTransaction()
            .replace(R.id.content_frame, fragment)
            .addToBackStack(null)
            .commit()
    }
    
    private fun showRemoveGroupScreen() {
        // Navigate to RemoveGroupFragment
        val fragment = RemoveGroupFragment()
        requireActivity().supportFragmentManager.beginTransaction()
            .replace(R.id.content_frame, fragment)
            .addToBackStack(null)
            .commit()
    }
    
    private fun logoutUser() {
        // Logout the user and redirect to login screen
        sessionManager.logoutUser()
        val intent = Intent(requireContext(), LoginActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        requireActivity().finish()
    }
    
    private fun loadUserAvatars() {
        val db = FirebaseFirestore.getInstance()
        
        lifecycleScope.launch {
            try {
                val usersList = mutableListOf<AdminForChat>()
                
                // Query depends on user type
                val query = if (userType == "Admin1") {
                    // Admin1 sees all users
                    db.collection("users")
                } else {
                    // Admin2 and Student see only Admin1 users
                    db.collection("users").whereEqualTo("userType", "Admin1")
                }
                
                val snapshot = query.get().await()
                
                for (document in snapshot.documents) {
                    val rollNumber = document.id
                    val name = document.getString("name") ?: "Unknown"
                    val type = document.getString("userType") ?: "Unknown"
                    val imageUrl = document.getString("imageUrl") ?: ""
                    
                    // Skip current user
                    if (rollNumber != userRollNumber) {
                        // In a real app, these would be determined by querying messages
                        val hasImportantMessages = false
                        val hasUnreadMessages = false
                        
                        usersList.add(AdminForChat(
                            rollNumber = rollNumber,
                            name = name,
                            userType = type,
                            imageUrl = imageUrl,
                            hasImportantMessages = hasImportantMessages,
                            hasUnreadMessages = hasUnreadMessages
                        ))
                    }
                }
                
                Log.d(TAG, "Loaded ${usersList.size} users for avatars")
                
                // Update UI on main thread
                val adapter = UserAvatarAdapter(requireContext(), usersList, sessionManager)
                recyclerUserAvatars.adapter = adapter
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading user avatars", e)
                Toast.makeText(requireContext(), "Failed to load users", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    private fun loadCommunities() {
        val db = FirebaseFirestore.getInstance()
        
        lifecycleScope.launch {
            try {
                val groupsList = mutableListOf<Group>()
                val currentUserId = sessionManager.fetchUserId()
                
                // Get groups where the current user is a member
                val snapshot = db.collection("groups")
                    .whereArrayContains("participants", currentUserId)
                    .get()
                    .await()
                
                for (document in snapshot.documents) {
                    try {
                        val group = document.toObject(Group::class.java)
                        if (group != null) {
                            group.id = document.id // Ensure ID is set
                            groupsList.add(group)
                        }
                    } catch (e: Exception) {
                        Log.e(TAG, "Error parsing group document ${document.id}", e)
                    }
                }
                
                Log.d(TAG, "Loaded ${groupsList.size} communities")
                
                // Update UI on main thread
                val adapter = CommunitiesAdapter(requireContext(), groupsList)
                recyclerCommunities.adapter = adapter
                
            } catch (e: Exception) {
                Log.e(TAG, "Error loading communities", e)
                Toast.makeText(requireContext(), "Failed to load communities", Toast.LENGTH_SHORT).show()
            }
        }
    }
    
    // Add this new method to refresh data from Firebase
    private fun refreshData() {
        Toast.makeText(requireContext(), "Refreshing data...", Toast.LENGTH_SHORT).show()
        
        // Reload user avatars and communities from Firebase
        loadUserAvatars()
        loadCommunities()
    }
    
    // Method to show unread messages popup
    private fun showUnreadMessagesPopup() {
        // Inflate the popup layout
        val popupView = LayoutInflater.from(requireContext()).inflate(R.layout.popup_unread_messages, null)
        
        // Create and configure the popup window
        val popupWindow = PopupWindow(
            popupView,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT,
            true
        )
        
        // Get references to RecyclerViews
        val recyclerDirectMessages = popupView.findViewById<RecyclerView>(R.id.recycler_direct_messages)
        val recyclerGroupMessages = popupView.findViewById<RecyclerView>(R.id.recycler_group_messages)
        val textNoDirectMessages = popupView.findViewById<TextView>(R.id.text_no_direct_messages)
        val textNoGroupMessages = popupView.findViewById<TextView>(R.id.text_no_group_messages)
        
        // Set up RecyclerView layouts
        recyclerDirectMessages.layoutManager = LinearLayoutManager(requireContext())
        recyclerGroupMessages.layoutManager = LinearLayoutManager(requireContext())
        
        // Create adapters
        val directMessageAdapter = com.phad.chatapp.adapters.UnreadMessageAdapter(requireContext(), false)
        val groupMessageAdapter = com.phad.chatapp.adapters.UnreadMessageAdapter(requireContext(), true)
        
        // Set adapters
        recyclerDirectMessages.adapter = directMessageAdapter
        recyclerGroupMessages.adapter = groupMessageAdapter
        
        // Fetch unread messages
        lifecycleScope.launch {
            val repo = com.phad.chatapp.repositories.UnreadMessageRepository(requireContext())
            val (directMessages, groupMessages) = repo.fetchAllUnreadMessages()
            
            // Update direct messages adapter
            directMessageAdapter.updateMessages(directMessages)
            if (directMessages.isEmpty()) {
                textNoDirectMessages.visibility = View.VISIBLE
                recyclerDirectMessages.visibility = View.GONE
            } else {
                textNoDirectMessages.visibility = View.GONE
                recyclerDirectMessages.visibility = View.VISIBLE
            }
            
            // Update group messages adapter
            groupMessageAdapter.updateMessages(groupMessages)
            if (groupMessages.isEmpty()) {
                textNoGroupMessages.visibility = View.VISIBLE
                recyclerGroupMessages.visibility = View.GONE
            } else {
                textNoGroupMessages.visibility = View.GONE
                recyclerGroupMessages.visibility = View.VISIBLE
            }
        }
        
        // Show popup at center of screen
        popupWindow.showAtLocation(requireView(), Gravity.CENTER, 0, 0)
    }
    
    /**
     * Shows a search dialog for finding and messaging users
     */
    private fun showSearchDialog() {
        // Create a dialog
        val dialog = Dialog(requireContext(), android.R.style.Theme_Material_Dialog_NoActionBar)
        dialog.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog.setContentView(R.layout.dialog_user_search)
        
        // Set dialog window properties
        val window = dialog.window
        if (window != null) {
            window.setLayout(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            )
            window.setGravity(Gravity.CENTER)
            window.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
        }
        
        // Get views from the dialog
        val editSearch = dialog.findViewById<EditText>(R.id.edit_search)
        val btnClearSearch = dialog.findViewById<ImageButton>(R.id.btn_clear_search)
        val progressSearch = dialog.findViewById<ProgressBar>(R.id.progress_search)
        val recyclerSearchResults = dialog.findViewById<RecyclerView>(R.id.recycler_search_results)
        val textNoResults = dialog.findViewById<TextView>(R.id.text_no_results)
        
        // Set up RecyclerView
        recyclerSearchResults.layoutManager = LinearLayoutManager(requireContext())
        
        // Create adapter for search results
        val searchAdapter = UserSearchAdapter(requireContext(), emptyList(), sessionManager) { user ->
            // Handle user selection - navigate to chat
            dialog.dismiss()
            navigateToChat(user)
        }
        
        recyclerSearchResults.adapter = searchAdapter
        
        // Set up search input listeners
        editSearch.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val query = s?.toString() ?: ""
                
                // Show/hide clear button
                btnClearSearch.visibility = if (query.isEmpty()) View.GONE else View.VISIBLE
                
                // Perform search if query has at least 1 character
                if (query.length >= 1) {
                    performSearch(query, searchAdapter, progressSearch, textNoResults, recyclerSearchResults)
                } else {
                    // Clear results if query is empty
                    searchAdapter.updateData(emptyList())
                    textNoResults.visibility = View.GONE
                }
            }
            
            override fun afterTextChanged(s: Editable?) {}
        })
        
        // Set up action search listener
        editSearch.setOnEditorActionListener { _, actionId, _ ->
            if (actionId == EditorInfo.IME_ACTION_SEARCH) {
                val query = editSearch.text.toString().trim()
                if (query.isNotEmpty()) {
                    performSearch(query, searchAdapter, progressSearch, textNoResults, recyclerSearchResults)
                    
                    // Hide keyboard
                    val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
                    imm.hideSoftInputFromWindow(editSearch.windowToken, 0)
                }
                return@setOnEditorActionListener true
            }
            false
        }
        
        // Set up clear button
        btnClearSearch.setOnClickListener {
            editSearch.setText("")
            
            // Hide keyboard
            val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
            imm.hideSoftInputFromWindow(editSearch.windowToken, 0)
        }
        
        // Show dialog
        dialog.show()
        
        // Focus on search input and show keyboard
        editSearch.requestFocus()
        val imm = requireContext().getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(editSearch, InputMethodManager.SHOW_IMPLICIT)
    }
    
    /**
     * Perform search for users matching the query
     */
    private fun performSearch(
        query: String,
        adapter: UserSearchAdapter,
        progressBar: ProgressBar,
        noResultsText: TextView,
        recyclerView: RecyclerView
    ) {
        // Show loading progress
        progressBar.visibility = View.VISIBLE
        noResultsText.visibility = View.GONE
        
        // Perform search in Firestore
        lifecycleScope.launch {
            try {
                val db = FirebaseFirestore.getInstance()
                
                // Define query based on user type
                val firestoreQuery = if (userType == "Admin1") {
                    // Admin1 can search all users
                    db.collection("users")
                } else {
                    // Other users can only search Admin1 users
                    db.collection("users").whereEqualTo("userType", "Admin1")
                }
                
                // Execute query
                val snapshot = firestoreQuery.get().await()
                
                // Process results and filter by query string
                val searchResults = mutableListOf<AdminForChat>()
                
                for (document in snapshot.documents) {
                    val rollNumber = document.id
                    val name = document.getString("name") ?: ""
                    val type = document.getString("userType") ?: ""
                    val imageUrl = document.getString("imageUrl") ?: ""
                    
                    // Skip current user
                    if (rollNumber != userRollNumber) {
                        // Check if name or roll number matches the query (case insensitive)
                        if (name.contains(query, ignoreCase = true) || 
                            rollNumber.contains(query, ignoreCase = true)) {
                            
                            searchResults.add(
                                AdminForChat(
                                    rollNumber = rollNumber,
                                    name = name,
                                    userType = type,
                                    imageUrl = imageUrl
                                )
                            )
                        }
                    }
                }
                
                // Sort results by name
                val sortedResults = searchResults.sortedBy { it.name }
                
                // Update UI
                progressBar.visibility = View.GONE
                
                if (sortedResults.isEmpty()) {
                    noResultsText.visibility = View.VISIBLE
                    recyclerView.visibility = View.GONE
                } else {
                    noResultsText.visibility = View.GONE
                    recyclerView.visibility = View.VISIBLE
                    adapter.updateData(sortedResults)
                }
                
            } catch (e: Exception) {
                // Handle error
                Log.e(TAG, "Error searching users", e)
                progressBar.visibility = View.GONE
                noResultsText.text = "Error searching. Try again."
                noResultsText.visibility = View.VISIBLE
                recyclerView.visibility = View.GONE
            }
        }
    }
    
    /**
     * Navigate to chat with the selected user
     */
    private fun navigateToChat(user: AdminForChat) {
        val intent = Intent(requireContext(), ChatActivity::class.java).apply {
            putExtra("currentUserRollNumber", userRollNumber)
            putExtra("currentUserName", sessionManager.fetchUserName())
            putExtra("otherUserRollNumber", user.rollNumber)
            putExtra("otherUserName", user.name)
        }
        startActivity(intent)
    }
} 