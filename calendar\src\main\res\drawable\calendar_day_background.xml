<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state -->
    <item android:state_selected="true">
        <shape android:shape="oval">
            <solid android:color="@color/calendar_selected_day_bg" />
        </shape>
    </item>
    <!-- Today state -->
    <item android:state_activated="true">
        <shape android:shape="oval">
            <solid android:color="@color/calendar_today_bg" />
        </shape>
    </item>
    <!-- Normal state -->
    <item>
        <shape android:shape="oval">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector> 